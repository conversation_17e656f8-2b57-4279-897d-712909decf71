#!/usr/bin/env python3
"""
Deploy Claims Web Server to Internet
Exposes the local web server to the internet for external access
"""

import asyncio
import logging
import subprocess
import sys
import time
from pathlib import Path
from typing import Optional

logger = logging.getLogger(__name__)

class WebDeployer:
    """Deploy web server to internet"""
    
    def __init__(self, local_port: int = 8080):
        self.local_port = local_port
        self.ngrok_process = None
        self.public_url = None
        
    async def deploy_with_ngrok(self) -> Optional[str]:
        """Deploy using ngrok tunnel"""
        try:
            logger.info("Starting ngrok tunnel...")
            
            # Check if ngrok is installed
            if not self._check_ngrok_installed():
                logger.error("ngrok not found. Please install ngrok first.")
                logger.info("Download from: https://ngrok.com/download")
                return None
            
            # Start ngrok tunnel
            self.ngrok_process = subprocess.Popen([
                'ngrok', 'http', str(self.local_port),
                '--log=stdout'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Wait for tunnel to be ready
            await asyncio.sleep(3)
            
            # Get public URL
            self.public_url = await self._get_ngrok_url()
            
            if self.public_url:
                logger.info(f"✅ Web server deployed successfully!")
                logger.info(f"🌐 Public URL: {self.public_url}")
                logger.info(f"📱 Tracking page: {self.public_url}/track/CLAIM-2025-ABC12345")
                logger.info(f"👨‍💼 Agent review: {self.public_url}/review/CLAIM-2025-ABC12345")
                logger.info(f"👨‍💼 Manager approval: {self.public_url}/approve/CLAIM-2025-ABC12345")
                return self.public_url
            else:
                logger.error("Failed to get ngrok URL")
                return None
                
        except Exception as e:
            logger.error(f"Error deploying with ngrok: {e}")
            return None
    
    def _check_ngrok_installed(self) -> bool:
        """Check if ngrok is installed"""
        try:
            result = subprocess.run(['ngrok', 'version'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    async def _get_ngrok_url(self) -> Optional[str]:
        """Get ngrok public URL"""
        try:
            # Get ngrok API info
            result = subprocess.run([
                'curl', '-s', 'http://localhost:4040/api/tunnels'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                import json
                tunnels = json.loads(result.stdout)
                if tunnels and 'tunnels' in tunnels:
                    for tunnel in tunnels['tunnels']:
                        if tunnel['proto'] == 'https':
                            return tunnel['public_url']
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting ngrok URL: {e}")
            return None
    
    async def deploy_with_cloudflare_tunnel(self) -> Optional[str]:
        """Deploy using Cloudflare Tunnel (alternative)"""
        try:
            logger.info("Setting up Cloudflare Tunnel...")
            
            # This would require cloudflared to be installed
            # For now, return instructions
            logger.info("Cloudflare Tunnel deployment requires cloudflared")
            logger.info("Install: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/")
            logger.info("Then run: cloudflared tunnel --url http://localhost:8080")
            
            return None
            
        except Exception as e:
            logger.error(f"Error with Cloudflare Tunnel: {e}")
            return None
    
    def stop_tunnel(self):
        """Stop the tunnel"""
        if self.ngrok_process:
            self.ngrok_process.terminate()
            logger.info("Tunnel stopped")

async def main():
    """Main deployment function"""
    try:
        deployer = WebDeployer(local_port=8080)
        
        print("🚀 Claims Web Server Deployment")
        print("=" * 40)
        
        # Check if local server is running
        print("1. Checking if local server is running...")
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:8080') as response:
                    if response.status == 200:
                        print("✅ Local server is running")
                    else:
                        print("❌ Local server not responding properly")
                        return
        except Exception as e:
            print(f"❌ Local server not running: {e}")
            print("Please start the claims workflow system first:")
            print("python main.py start")
            return
        
        # Deploy to internet
        print("\n2. Deploying to internet...")
        public_url = await deployer.deploy_with_ngrok()
        
        if public_url:
            print("\n🎉 DEPLOYMENT SUCCESSFUL!")
            print("=" * 40)
            print(f"Public URL: {public_url}")
            print(f"Tracking: {public_url}/track/CLAIM-2025-ABC12345")
            print(f"Agent Review: {public_url}/review/CLAIM-2025-ABC12345")
            print(f"Manager Approval: {public_url}/approve/CLAIM-2025-ABC12345")
            print("\nPress Ctrl+C to stop the tunnel")
            
            # Keep running
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Stopping tunnel...")
                deployer.stop_tunnel()
                print("✅ Tunnel stopped")
        else:
            print("❌ Deployment failed")
            
    except Exception as e:
        logger.error(f"Deployment error: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 