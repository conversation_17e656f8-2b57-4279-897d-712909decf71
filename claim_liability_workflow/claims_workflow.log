2025-06-24 15:22:48,049 - __main__ - INFO - Starting Claims Liability Workflow System...
2025-06-24 15:22:48,049 - src.email_monitor - INFO - Email monitor <NAME_EMAIL>
2025-06-24 15:22:48,049 - src.document_processor - INFO - Document processor initialized
2025-06-24 15:22:48,049 - src.zendesk_integration - INFO - Zendesk integration initialized for d3v-rozieai5417
2025-06-24 15:22:48,049 - src.slack_integration - INFO - Slack integration initialized for channel C092M4E1SH0
2025-06-24 15:22:48,049 - src.human_layer_integration - INFO - HumanLayer integration initialized
2025-06-24 15:22:48,049 - src.database_manager - INFO - Database manager initialized
2025-06-24 15:22:48,049 - src.notification_service - INFO - Notification service initialized
2025-06-24 15:22:48,049 - src.workflow_orchestrator - INFO - Claims Workflow Orchestrator initialized
2025-06-24 15:22:48,049 - src.workflow_orchestrator - INFO - Starting Claims Workflow Orchestrator...
2025-06-24 15:22:48,049 - src.database_manager - ERROR - Failed to initialize database: No module named 'supabase'
2025-06-24 15:22:48,049 - src.workflow_orchestrator - ERROR - Failed to start orchestrator: No module named 'supabase'
2025-06-24 15:22:48,049 - __main__ - ERROR - Failed to start Claims Liability Workflow System: No module named 'supabase'
2025-06-24 15:22:48,049 - __main__ - INFO - Stopping Claims Liability Workflow System...
2025-06-24 15:22:48,049 - src.email_monitor - INFO - Email monitoring stopped
2025-06-24 15:22:48,049 - src.workflow_orchestrator - INFO - Claims Workflow Orchestrator stopped
2025-06-24 15:22:48,050 - __main__ - INFO - Claims Liability Workflow System stopped
2025-06-24 15:23:02,426 - __main__ - INFO - Starting Claims Liability Workflow System...
2025-06-24 15:23:02,427 - src.email_monitor - INFO - Email monitor <NAME_EMAIL>
2025-06-24 15:23:02,427 - src.document_processor - INFO - Document processor initialized
2025-06-24 15:23:02,427 - src.zendesk_integration - INFO - Zendesk integration initialized for d3v-rozieai5417
2025-06-24 15:23:02,427 - src.slack_integration - INFO - Slack integration initialized for channel C092M4E1SH0
2025-06-24 15:23:02,427 - src.human_layer_integration - INFO - HumanLayer integration initialized
2025-06-24 15:23:02,427 - src.database_manager - INFO - Database manager initialized
2025-06-24 15:23:02,427 - src.notification_service - INFO - Notification service initialized
2025-06-24 15:23:02,427 - src.workflow_orchestrator - INFO - Claims Workflow Orchestrator initialized
2025-06-24 15:23:02,427 - src.workflow_orchestrator - INFO - Starting Claims Workflow Orchestrator...
2025-06-24 15:23:03,509 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=count&limit=1 "HTTP/2 200 OK"
2025-06-24 15:23:03,513 - src.database_manager - INFO - Database health check passed
2025-06-24 15:23:03,514 - src.database_manager - INFO - Database connection initialized successfully
2025-06-24 15:23:03,514 - src.email_monitor - INFO - Starting email monitoring...
2025-06-24 15:23:03,514 - src.email_monitor - INFO - Email monitoring started successfully
2025-06-24 15:23:03,514 - src.workflow_orchestrator - INFO - Claims Workflow Orchestrator started successfully
2025-06-24 15:23:03,514 - __main__ - INFO - Claims Liability Workflow System started successfully
2025-06-24 15:23:05,850 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:24:06,278 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:25:06,626 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:26:07,025 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:27:07,433 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:28:07,789 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:29:08,157 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:30:08,533 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:31:10,398 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:32:10,754 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:33:11,156 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:34:11,526 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:35:12,398 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:36:13,018 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:37:13,437 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:38:13,850 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:39:14,251 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:40:14,684 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&status=in.%28received%2Cdocuments_processing%2Cai_analysis%2Chuman_review%2Cpending_approval%29&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 15:40:53,898 - __main__ - INFO - Received signal 2
2025-06-24 15:40:53,900 - __main__ - INFO - Stopping Claims Liability Workflow System...
2025-06-24 15:40:53,900 - src.email_monitor - INFO - Email monitoring stopped
2025-06-24 15:40:53,901 - src.workflow_orchestrator - INFO - Claims Workflow Orchestrator stopped
2025-06-24 15:40:53,901 - __main__ - INFO - Claims Liability Workflow System stopped
2025-06-24 17:07:17,824 - __main__ - INFO - Testing Claims Liability Workflow System components...
2025-06-24 17:07:17,824 - __main__ - INFO - Configuration validation passed
2025-06-24 17:07:17,824 - src.email_monitor - INFO - Email monitor <NAME_EMAIL>
2025-06-24 17:07:19,941 - src.email_monitor - INFO - Email connection test successful
2025-06-24 17:07:19,941 - __main__ - INFO - Email connection test passed
2025-06-24 17:07:19,941 - src.human_layer_integration - INFO - HumanLayer integration initialized
2025-06-24 17:07:21,230 - src.human_layer_integration - ERROR - HumanLayer connection test failed: 403
2025-06-24 17:07:21,231 - __main__ - WARNING - HumanLayer connection test failed (using fallback)
2025-06-24 17:07:21,231 - src.zendesk_integration - INFO - Zendesk integration initialized for d3v-rozieai5417
2025-06-24 17:07:21,981 - src.zendesk_integration - INFO - Zendesk connection test successful
2025-06-24 17:07:21,981 - __main__ - INFO - Zendesk connection test passed
2025-06-24 17:07:22,170 - __main__ - INFO - OpenAI API key configured
2025-06-24 17:07:22,170 - __main__ - INFO - System component tests completed
2025-06-24 17:07:22,170 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x112c406e0>
2025-06-24 17:07:22,170 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x112badb50>, 28696.35696325)])']
connector: <aiohttp.connector.TCPConnector object at 0x112c402f0>
2025-06-24 17:07:22,170 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x112bc2210>
2025-06-24 17:07:22,170 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x112c20410>, 28697.226409875)])']
connector: <aiohttp.connector.TCPConnector object at 0x112bc1d10>
