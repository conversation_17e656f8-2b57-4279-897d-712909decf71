# 🌐 Claims Workflow Web Deployment Guide

This guide explains how to expose the claims workflow web interfaces to the internet.

## 🚀 Quick Start

### Option 1: Start with Internet Deployment (Recommended)

```bash
# Install dependencies
pip install -r requirements.txt

# Start system with internet deployment
python start_with_web.py --deploy
```

This will:
1. Start the claims workflow system
2. Start the web server on port 8080
3. Deploy to internet using ngrok
4. Provide public URLs for all interfaces

### Option 2: Local Only

```bash
# Start system locally only
python start_with_web.py
```

## 📋 Prerequisites

### For Internet Deployment (ngrok)
1. **Install ngrok**: Download from https://ngrok.com/download
2. **Sign up for free account**: Get your authtoken
3. **Configure ngrok**: `ngrok config add-authtoken YOUR_TOKEN`

### For Cloudflare Deployment
1. **Install cloudflared**: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/
2. **Configure tunnel**: Follow Cloudflare documentation

## 🌍 Deployment Options

### 1. ngrok Tunnel (Easiest)
- ✅ Free tier available
- ✅ Automatic HTTPS
- ✅ Public URLs immediately
- ❌ URLs change on restart (free tier)

```bash
# Automatic deployment
python start_with_web.py --deploy

# Manual deployment
python deploy_web.py
```

### 2. Cloudflare Tunnel (Production)
- ✅ Permanent URLs
- ✅ Enterprise-grade security
- ✅ Custom domains
- ❌ Requires Cloudflare account

```bash
# Install cloudflared
brew install cloudflare/cloudflare/cloudflared  # macOS
# or download from Cloudflare website

# Create tunnel
cloudflared tunnel create claims-workflow

# Configure tunnel
cloudflared tunnel route dns claims-workflow claims.yourdomain.com

# Start tunnel
cloudflared tunnel run claims-workflow
```

### 3. Cloudflare Pages (Static)
- ✅ Permanent URLs
- ✅ Global CDN
- ❌ Requires manual deployment

```bash
# Deploy static files to Cloudflare Pages
# Upload web/ folder contents to Cloudflare Pages
```

## 🔗 Generated URLs

When deployed, the system generates these URLs:

### Customer Interface
- **Tracking Page**: `https://your-domain.com/track/CLAIM-2025-ABC12345`
- **Purpose**: Customers track their claim status

### Agent Interface  
- **Review Page**: `https://your-domain.com/review/CLAIM-2025-ABC12345`
- **Purpose**: Agents review claims and make decisions

### Manager Interface
- **Approval Page**: `https://your-domain.com/approve/CLAIM-2025-ABC12345`
- **Purpose**: Managers approve/reject agent decisions

## 📧 Email Integration

The system automatically:
1. **Monitors emails** sent to `<EMAIL>`
2. **Generates tracking links** in acknowledgment emails
3. **Sends links to customers** with their claim ID
4. **Provides agent/manager links** via Slack notifications

## 🔧 Configuration

### Environment Variables
```bash
# Web server configuration
TRACKING_URL=https://your-domain.com/track
REVIEW_URL=https://your-domain.com/review  
APPROVAL_URL=https://your-domain.com/approve

# For local development
TRACKING_URL=http://localhost:8080/track
REVIEW_URL=http://localhost:8080/review
APPROVAL_URL=http://localhost:8080/approve
```

### Custom Domain Setup
1. **Get domain**: Purchase or use existing domain
2. **Configure DNS**: Point to your deployment
3. **Update config**: Set URLs in environment variables
4. **SSL certificate**: Automatic with Cloudflare/ngrok

## 🛠️ Troubleshooting

### Common Issues

**ngrok not found**
```bash
# Install ngrok
brew install ngrok  # macOS
# or download from ngrok.com
```

**Port 8080 already in use**
```bash
# Change port in start_with_web.py
# Update local_port parameter
```

**Web server not starting**
```bash
# Check dependencies
pip install -r requirements.txt

# Check logs
tail -f claims_workflow.log
```

**Tunnel not working**
```bash
# Check ngrok status
curl http://localhost:4040/api/tunnels

# Restart tunnel
python deploy_web.py
```

## 🔒 Security Considerations

### Production Deployment
- ✅ Use HTTPS (automatic with ngrok/Cloudflare)
- ✅ Implement authentication for agent/manager interfaces
- ✅ Rate limiting on API endpoints
- ✅ Input validation on all forms
- ✅ Regular security updates

### Development
- ⚠️ ngrok URLs are public (free tier)
- ⚠️ No authentication on local development
- ⚠️ Debug information may be exposed

## 📊 Monitoring

### Health Checks
```bash
# Check web server
curl http://localhost:8080/

# Check API endpoints
curl http://localhost:8080/api/claim/test

# Check ngrok tunnel
curl http://localhost:4040/api/tunnels
```

### Logs
```bash
# System logs
tail -f claims_workflow.log

# Web server logs
# Check console output for web server messages
```

## 🚀 Production Checklist

- [ ] Custom domain configured
- [ ] SSL certificate active
- [ ] Authentication implemented
- [ ] Rate limiting configured
- [ ] Monitoring setup
- [ ] Backup strategy
- [ ] Error handling tested
- [ ] Performance optimized

## 📞 Support

For deployment issues:
1. Check the logs: `tail -f claims_workflow.log`
2. Verify dependencies: `pip list`
3. Test connectivity: `curl http://localhost:8080/`
4. Check ngrok status: `curl http://localhost:4040/api/tunnels` 