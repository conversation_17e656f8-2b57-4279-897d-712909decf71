# Use official Python 3.11 image
FROM python:3.11-slim

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --upgrade pip && pip install --no-cache-dir -r requirements.txt

# Copy the entire application code (including pre-generated baml_client)
COPY . .

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Expose port for web server
EXPOSE 8080

# Entrypoint
CMD ["python", "main.py", "start"]