# Use official Python 3.11 image
FROM python:3.11-slim

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install BAML CLI
RUN pip install --upgrade pip && pip install baml

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy BAML source files and build the Python client
COPY baml_src/ ./baml_src/
RUN baml build

# Copy the rest of the application code (including generated baml_client)
COPY . .

# Set environment variables (for local dev, .env will be used by python-dotenv)
ENV PYTHONUNBUFFERED=1

# Expose port if needed (uncomment if running a web server)
# EXPOSE 8000

# Entrypoint
CMD ["python", "main.py", "start"] 