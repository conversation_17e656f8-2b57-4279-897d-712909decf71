# Use official Python 3.11 image
FROM python:3.11-slim

# Set work directory
WORKDIR /app

# Install system dependencies including Node.js for BAML CLI
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    git \
    curl \
    && curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# Install BAML CLI via npm
RUN npm install -g @boundaryml/baml

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --upgrade pip && pip install --no-cache-dir -r requirements.txt

# Copy BAML source files and build the Python client
COPY baml_src/ ./baml_src/
RUN baml build

# Copy the rest of the application code (including generated baml_client)
COPY . .

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Expose port for web server
EXPOSE 8080

# Entrypoint
CMD ["python", "main.py", "start"]