###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import baml_py
from enum import Enum

from pydantic import BaseModel, ConfigDict

from typing_extensions import TypeAlia<PERSON>, Literal
from typing import Dict, Generic, List, Optional, TypeVar, Union


T = TypeVar('T')
CheckName = TypeVar('CheckName', bound=str)

class Check(BaseModel):
    name: str
    expression: str
    status: str
class Checked(BaseModel, Generic[T,CheckName]):
    value: T
    checks: Dict[CheckName, Check]

def get_checks(checks: Dict[CheckName, Check]) -> List[Check]:
    return list(checks.values())

def all_succeeded(checks: Dict[CheckName, Check]) -> bool:
    return all(check.status == "succeeded" for check in get_checks(checks))



class AgentAssignment(BaseModel):
    intent: Literal["assign_agent"]
    claimId: str
    agentType: Union[Literal["claims_adjuster"], Literal["senior_adjuster"], Literal["fraud_investigator"], Literal["manager"], Literal["legal_counsel"]]
    priority: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]
    reason: str
    estimatedProcessingTime: str

class AgentDecision(BaseModel):
    intent: Literal["agent_decision"]
    claimId: str
    decision: Union[Literal["approve"], Literal["deny"], Literal["investigate"], Literal["request_more_info"], Literal["escalate"]]
    settlementAmount: Optional[float] = None
    reasoning: str
    nextSteps: List[str]
    requiresManagerApproval: bool

class AgentResponse(BaseModel):
    agentId: str
    responseType: Union[Literal["approval"], Literal["denial"], Literal["request_info"], Literal["escalation"], Literal["status_update"]]
    decision: str
    reasoning: str
    nextActions: List[str]
    confidenceLevel: Union[Literal["low"], Literal["medium"], Literal["high"]]

class ClaimAnalysis(BaseModel):
    classification: "ClaimClassification"
    fraudAnalysis: "FraudAnalysis"
    coverageAnalysis: "CoverageAnalysis"
    documents: List["DocumentAnalysis"]
    liabilityAssessment: "LiabilityAssessment"
    lossQuantum: "LossQuantumAssessment"
    overallRecommendation: Union[Literal["approve"], Literal["deny"], Literal["investigate"], Literal["request_more_info"]]
    reasoning: str
    nextSteps: List[str]
    priority: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]
    businessContext: str

class ClaimClassification(BaseModel):
    claimType: Union[Literal["auto"], Literal["property"], Literal["liability"], Literal["professional"], Literal["bodily_injury"]]
    severity: Union[Literal["minor"], Literal["major"], Literal["catastrophic"]]
    estimatedValue: float
    confidenceScore: float

class CoverageAnalysis(BaseModel):
    policyValid: bool
    coverageType: str
    deductible: float
    coverageLimit: float
    exclusions: List[str]
    recommendation: Union[Literal["covered"], Literal["partially_covered"], Literal["not_covered"]]
    policyPeriod: str
    termsConditions: str

class CustomerCommunication(BaseModel):
    intent: Literal["customer_communication"]
    claimId: str
    communicationType: Union[Literal["acknowledgment"], Literal["status_update"], Literal["decision_notification"], Literal["document_request"]]
    message: str
    attachments: List[str]
    urgency: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]

class DocumentAnalysis(BaseModel):
    documentType: Union[Literal["police_report"], Literal["medical_record"], Literal["receipt"], Literal["photo"], Literal["insurance_certificate"], Literal["other"]]
    extractedData: Dict[str, str]
    completeness: float
    quality: float
    requiredForClaim: bool
    authenticityAssessment: str

class DocumentExtraction(BaseModel):
    policyNumber: Optional[str] = None
    claimAmount: Optional[float] = None
    incidentDate: Optional[str] = None
    location: Optional[str] = None
    partiesInvolved: List[str]
    damages: List[str]
    witnesses: List[str]
    policeReportNumber: Optional[str] = None
    medicalInformation: List[str]

class DocumentProcessing(BaseModel):
    intent: Literal["process_documents"]
    claimId: str
    documentType: Union[Literal["police_report"], Literal["medical_record"], Literal["receipt"], Literal["photo"], Literal["insurance_certificate"], Literal["other"]]
    processingAction: Union[Literal["ocr"], Literal["classify"], Literal["validate"], Literal["extract_data"]]
    priority: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]
    expectedOutput: str

class EmailClassification(BaseModel):
    isClaim: bool
    claimType: Union[Literal["auto"], Literal["property"], Literal["liability"], Literal["professional"], Literal["bodily_injury"], Literal["general_inquiry"], Literal["other"]]
    urgency: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]
    requiresImmediateResponse: bool
    suggestedResponse: str

class EscalateClaim(BaseModel):
    intent: Literal["escalate_claim"]
    claimId: str
    escalationReason: str
    escalationLevel: Union[Literal["manager"], Literal["director"], Literal["legal"], Literal["fraud_team"]]
    urgency: Union[Literal["high"], Literal["urgent"]]
    additionalContext: str

class FraudAnalysis(BaseModel):
    riskScore: int
    redFlags: List[str]
    fraudIndicators: List[str]
    recommendation: Union[Literal["approve"], Literal["investigate"], Literal["deny"]]
    confidenceScore: float

class FraudInvestigation(BaseModel):
    intent: Literal["fraud_investigation"]
    claimId: str
    investigationType: Union[Literal["preliminary"], Literal["detailed"], Literal["surveillance"], Literal["background_check"]]
    redFlags: List[str]
    investigationSteps: List[str]
    estimatedDuration: str
    requiresExternalResources: bool

class LiabilityAssessment(BaseModel):
    faultDetermination: Union[Literal["clear_liability"], Literal["shared_liability"], Literal["no_liability"], Literal["unclear"]]
    faultPercentage: float
    contributingFactors: List[str]
    evidenceStrength: Union[Literal["strong"], Literal["moderate"], Literal["weak"]]
    recommendation: str
    canadianLiabilityContext: str

class LossQuantumAssessment(BaseModel):
    totalLossValue: float
    propertyDamageValue: float
    bodilyInjuryValue: float
    otherDamagesValue: float
    supportingEvidence: List[str]
    confidenceLevel: Union[Literal["low"], Literal["medium"], Literal["high"]]

class RequestMoreInformation(BaseModel):
    intent: Literal["request_more_info"]
    claimId: str
    requestedDocuments: List[str]
    questions: List[str]
    deadline: str
    urgency: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]

class TeamNotification(BaseModel):
    intent: Literal["team_notification"]
    claimId: str
    notificationType: Union[Literal["new_claim"], Literal["agent_assigned"], Literal["decision_made"], Literal["escalation"], Literal["urgent_alert"]]
    channel: Union[Literal["slack"], Literal["email"], Literal["both"]]
    message: str
    recipients: List[str]
    requiresAction: bool

class UpdateClaimStatus(BaseModel):
    intent: Literal["update_status"]
    claimId: str
    newStatus: Union[Literal["received"], Literal["under_review"], Literal["pending_documents"], Literal["approved"], Literal["denied"], Literal["investigation"], Literal["closed"]]
    statusNotes: str
    notifyCustomer: bool
    internalNotes: Optional[str] = None

class WorkflowCompletion(BaseModel):
    intent: Literal["workflow_complete"]
    claimId: str
    finalStatus: Union[Literal["approved"], Literal["denied"], Literal["closed"], Literal["referred"]]
    summary: str
    lessonsLearned: List[str]
    recommendations: List[str]
