#!/usr/bin/env python3
"""
Setup script for Claims Liability Workflow System
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_dependencies():
    """Install Python dependencies"""
    return run_command("pip install -r requirements.txt", "Installing Python dependencies")

def create_directories():
    """Create necessary directories"""
    directories = [
        "temp_attachments",
        "logs",
        "data",
        "baml_client"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def setup_environment():
    """Set up environment variables"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("📝 Creating .env file with default configuration...")
        
        env_content = """# Claims Liability Workflow System Configuration

# Database (Supabase)
SUPABASE_URL=https://tlduggpohclrgxbvuzhd.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MDEwMzgsImV4cCI6MjA2NjI3NzAzOH0.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MDEwMzgsImV4cCI6MjA2NjI3NzAzOH0.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk

# Email Configuration
CLAIMS_EMAIL=<EMAIL>
CLAIMS_EMAIL_PASSWORD=zgyqdymnzqetkvf
IMAP_SERVER=imap.gmail.com
IMAP_PORT=993

# AI Services
OPENAI_API_KEY=***************************************************
ZURICH_OCR_API_URL=https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process

# HumanLayer
HUMANLAYER_API_KEY=hl-a-XCV49qIfZRLqh8N1ra8ZhiQuYIXdXRpI1cGHRK052w0

# Zendesk
ZENDESK_SUBDOMAIN=d3v-rozieai5417
ZENDESK_EMAIL=<EMAIL>
ZENDESK_API_TOKEN=1gfty1sXmithKfIhjwWnUWmTXLrxlVAJEiGrNcF1

# Slack
SLACK_CLAIMS_CHANNEL=C092M4E1SH0

# Application
TRACKING_URL=http://localhost:8080
LOG_LEVEL=INFO
ENVIRONMENT=development
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("✅ .env file created with default configuration")
        print("⚠️  Please review and update the configuration as needed")
    else:
        print("✅ .env file already exists")

def generate_baml_client():
    """Generate BAML client code"""
    if Path("baml_src").exists():
        return run_command("baml generate", "Generating BAML client code")
    else:
        print("⚠️  BAML source files not found, skipping client generation")
        return True

def main():
    """Main setup function"""
    print("🚀 Setting up Claims Liability Workflow System...")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Set up environment
    setup_environment()
    
    # Generate BAML client
    generate_baml_client()
    
    print("=" * 50)
    print("✅ Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Review and update the .env file with your configuration")
    print("2. Test the system: python main.py test")
    print("3. Process a test claim: python main.py test-claim")
    print("4. Start the system: python main.py start")
    print("\n📚 For more information, see the README.md file")

if __name__ == "__main__":
    main() 