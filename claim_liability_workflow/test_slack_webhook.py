#!/usr/bin/env python3
"""
Test script for Slack webhook configuration
"""

import asyncio
import os
from dotenv import load_dotenv
from src.slack_integration import SlackIntegration

async def test_slack_webhook():
    """Test the Slack webhook configuration"""
    
    # Load environment variables
    load_dotenv()
    
    # Create configuration
    config = {
        'slack_claims_channel': os.getenv('SLACK_CLAIMS_CHANNEL', 'C092M4E1SH0'),
        'slack_webhook_url': os.getenv('SLACK_WEBHOOK_URL', ''),
        'slack_bot_token': os.getenv('SLACK_BOT_TOKEN', '')
    }
    
    print("🧪 Testing Slack Webhook Configuration...")
    print(f"Channel: {config['slack_claims_channel']}")
    print(f"Webhook URL: {config['slack_webhook_url'][:50]}..." if config['slack_webhook_url'] else "Webhook URL: Not configured")
    
    # Initialize Slack integration
    slack = SlackIntegration(config)
    
    try:
        # Test connection
        print("\n📡 Testing Slack connection...")
        success = await slack.test_connection()
        
        if success:
            print("✅ Slack webhook test successful!")
            
            # Send a test notification
            print("\n📢 Sending test notification...")
            notification_success = await slack.send_notification(
                channel=config['slack_claims_channel'],
                message="🧪 Test notification from Claims Liability Workflow System - Setup Complete!",
                requires_action=False
            )
            
            if notification_success:
                print("✅ Test notification sent successfully!")
            else:
                print("❌ Failed to send test notification")
                
        else:
            print("❌ Slack webhook test failed!")
            print("Please check your webhook URL configuration")
            
    except Exception as e:
        print(f"❌ Error testing Slack webhook: {e}")
        
    finally:
        await slack.close()

if __name__ == "__main__":
    asyncio.run(test_slack_webhook())
