#!/usr/bin/env python3
"""
Start Claims Workflow System with Web Server
Starts the complete system and optionally exposes it to the internet
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import ClaimsWorkflowSystem
from deploy_web import WebDeployer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def start_system_with_web(deploy_to_internet: bool = False):
    """Start the claims workflow system with web server"""
    try:
        print("🚀 Starting Claims Liability Workflow System")
        print("=" * 50)
        
        # Start the main system
        system = ClaimsWorkflowSystem()
        success = await system.start()
        
        if not success:
            print("❌ Failed to start the system")
            return
        
        print("✅ Claims workflow system started successfully")
        print("🌐 Web server running on http://localhost:8080")
        print("📧 Email monitoring active")
        print("🤖 AI analysis ready")
        print("📱 Zendesk integration active")
        print("💬 Slack notifications ready")
        
        if deploy_to_internet:
            print("\n🌍 Deploying to internet...")
            deployer = WebDeployer(local_port=8080)
            public_url = await deployer.deploy_with_ngrok()
            
            if public_url:
                print(f"\n🎉 INTERNET DEPLOYMENT SUCCESSFUL!")
                print("=" * 50)
                print(f"🌐 Public URL: {public_url}")
                print(f"📱 Customer Tracking: {public_url}/track/CLAIM-2025-ABC12345")
                print(f"👨‍💼 Agent Review: {public_url}/review/CLAIM-2025-ABC12345")
                print(f"👨‍💼 Manager Approval: {public_url}/approve/CLAIM-2025-ABC12345")
                print("\n📧 Send emails to: <EMAIL>")
                print("🔗 Links will be automatically generated and sent to customers")
            else:
                print("⚠️  Internet deployment failed, but local system is running")
                print("🌐 Local URLs:")
                print(f"   Tracking: http://localhost:8080/track/CLAIM-2025-ABC12345")
                print(f"   Agent Review: http://localhost:8080/review/CLAIM-2025-ABC12345")
                print(f"   Manager Approval: http://localhost:8080/approve/CLAIM-2025-ABC12345")
        else:
            print("\n🌐 Local URLs:")
            print(f"   Tracking: http://localhost:8080/track/CLAIM-2025-ABC12345")
            print(f"   Agent Review: http://localhost:8080/review/CLAIM-2025-ABC12345")
            print(f"   Manager Approval: http://localhost:8080/approve/CLAIM-2025-ABC12345")
        
        print("\n📧 To test the system, send an email to: <EMAIL>")
        print("🔄 The system will automatically process emails and generate tracking links")
        print("\nPress Ctrl+C to stop the system")
        
        # Keep the system running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping system...")
            await system.stop()
            print("✅ System stopped")
            
    except Exception as e:
        logger.error(f"Error starting system: {e}")
        print(f"❌ Error: {e}")

async def main():
    """Main function"""
    deploy_to_internet = "--deploy" in sys.argv
    
    if deploy_to_internet:
        print("🌍 Mode: Local + Internet Deployment")
    else:
        print("🏠 Mode: Local Only")
    
    await start_system_with_web(deploy_to_internet)

if __name__ == "__main__":
    asyncio.run(main()) 