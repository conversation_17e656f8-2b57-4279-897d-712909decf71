"""
Claims Liability Workflow Orchestrator
Coordinates the complete email-to-resolution process for Canadian liability claims
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import uuid

from baml_client.async_client import b
from baml_client.types import (
    ClaimAnalysis, EmailClassification, DocumentExtraction,
    AgentAssignment, AgentDecision, AgentResponse, RequestMoreInformation,
    EscalateClaim, UpdateClaimStatus, CustomerCommunication, TeamNotification,
    DocumentProcessing, FraudInvestigation, WorkflowCompletion
)
from typing import Union

from .email_monitor import EmailMonitor
from .document_processor import DocumentProcessor
from .zendesk_integration import ZendeskIntegration
from .slack_integration import SlackIntegration
from .human_layer_integration import HumanLayerIntegration
from .database_manager import DatabaseManager
from .notification_service import NotificationService
from .web_server import ClaimsWebServer

logger = logging.getLogger(__name__)

@dataclass
class ClaimData:
    """Data structure for claim information"""
    claim_id: str
    email_data: Dict[str, Any]
    classification: EmailClassification
    extracted_details: DocumentExtraction
    ai_analysis: Optional[ClaimAnalysis]
    status: str
    created_at: datetime
    updated_at: datetime
    zendesk_ticket_id: Optional[str] = None
    assigned_agent: Optional[str] = None
    manager_email: Optional[str] = None
    agent_decision: Optional[Dict[str, Any]] = None
    manager_decision: Optional[Dict[str, Any]] = None
    approval_status: Optional[str] = None
    final_decision: Optional[Dict[str, Any]] = None
    tracking_url: Optional[str] = None
    review_url: Optional[str] = None

class ClaimsWorkflowOrchestrator:
    """
    Orchestrates the complete claims processing workflow
    Implements staged review with manager approval
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_running = False
        self.active_claims = {}
        
        # Initialize services
        self.database = DatabaseManager(config)
        self.document_processor = DocumentProcessor(config)
        self.human_layer = HumanLayerIntegration(config)
        self.zendesk_integration = ZendeskIntegration(config)
        self.slack_integration = SlackIntegration(config)
        self.notification_service = NotificationService(config)
        
        # Initialize web server
        self.web_server = ClaimsWebServer(config, self.database, self)
        self.web_runner = None
        
        # Generate base URLs - use local server URLs
        self.base_tracking_url = config.get('tracking_url', 'http://localhost:8080/track')
        self.base_review_url = config.get('review_url', 'http://localhost:8080/review')
        self.base_approval_url = config.get('approval_url', 'http://localhost:8080/approve')
    
    async def start(self):
        """Start the workflow orchestrator"""
        try:
            logger.info("Starting Claims Workflow Orchestrator")
            self.is_running = True

            # Initialize database connection
            await self.database.initialize()

            # Start web server
            self.web_runner = await self.web_server.start(host='0.0.0.0', port=8080)

            # Start background tasks
            asyncio.create_task(self._monitor_active_claims())
            asyncio.create_task(self._process_pending_claims())

            logger.info("Claims Workflow Orchestrator started successfully")
            logger.info(f"Web server running on http://localhost:8080")

        except Exception as e:
            logger.error(f"Error starting workflow orchestrator: {e}")
            raise
    
    async def stop(self):
        """Stop the workflow orchestrator"""
        try:
            logger.info("Stopping Claims Workflow Orchestrator")
            self.is_running = False

            # Stop web server
            if self.web_runner:
                await self.web_server.stop(self.web_runner)

            # Close database connection
            await self.database.close()

            logger.info("Claims Workflow Orchestrator stopped")
        except Exception as e:
            logger.error(f"Error stopping workflow orchestrator: {e}")

    async def process_new_email(self, email_data: Dict[str, Any]):
        """
        Process a new incoming email
        New staged workflow: Email → Zendesk → DB → Acknowledgment → Processing → AI Analysis → Slack → Agent Review → Manager Approval → Finalization
        """
        try:
            claim_id = f"CLAIM-{datetime.now().year}-{str(uuid.uuid4())[:8].upper()}"
            logger.info(f"Processing new email for claim {claim_id}")
            
            # Generate URLs
            tracking_url = f"{self.base_tracking_url}/{claim_id}"
            review_url = f"{self.base_review_url}/{claim_id}"
            
            # Step 1: Classify the email
            classification = await self._classify_email(email_data)
            
            if not classification.isClaim:
                # Handle general inquiry
                await self._handle_general_inquiry(email_data, classification)
                return
            
            # Step 2: Create Zendesk ticket first
            zendesk_ticket = await self._create_initial_zendesk_ticket(email_data, claim_id)
            
            # Step 3: Create claim record
            claim_data = ClaimData(
                claim_id=claim_id,
                email_data=email_data,
                classification=classification,
                extracted_details=DocumentExtraction(
                    policyNumber="",
                    claimAmount=0.0,
                    incidentDate="",
                    location="",
                    partiesInvolved=[],
                    damages=[],
                    witnesses=[],
                    policeReportNumber="",
                    medicalInformation=[]
                ),
                ai_analysis=None,
                status="received",
                created_at=datetime.now(),
                updated_at=datetime.now(),
                zendesk_ticket_id=zendesk_ticket['id'],
                tracking_url=tracking_url,
                review_url=review_url
            )
            
            # Store in database
            await self.database.create_claim(claim_data)
            self.active_claims[claim_id] = claim_data
            
            # Step 4: Send immediate acknowledgment
            await self._send_acknowledgment(claim_data)
            
            # Step 5: Process documents if attachments exist
            if email_data.get('attachments'):
                await self._process_documents(claim_data)
            
            # Step 6: Extract claim details
            await self._extract_claim_details(claim_data)
            
            # Step 7: Perform AI analysis
            await self._perform_ai_analysis(claim_data)
            
            # Step 8: Send Slack notification for assignment
            await self._send_slack_assignment_notification(claim_data)
            
            logger.info(f"Claim {claim_id} processed successfully - ready for agent assignment")
            
        except Exception as e:
            logger.error(f"Error processing new email: {e}")
            # Send error notification
            await self._send_error_notification(email_data, str(e))

    async def _create_initial_zendesk_ticket(self, email_data: Dict[str, Any], claim_id: str) -> Dict[str, Any]:
        """Create initial Zendesk ticket with email details"""
        try:
            ticket_data = {
                'subject': f"[{claim_id}] New Claim Received - {email_data.get('subject', 'No Subject')}",
                'description': f"""
**New Claim Received**

Claim ID: {claim_id}
Customer: {email_data.get('from_name', 'Unknown')}
Email: {email_data.get('from_email', 'Unknown')}
Subject: {email_data.get('subject', 'No Subject')}

**Email Content:**
{email_data.get('body', 'No content')}

**Attachments:** {len(email_data.get('attachments', []))} files

**Status:** Initial processing started
                """,
                'priority': 'normal',
                'tags': ['aria', 'claims', 'new', 'processing'],
                'requester_email': email_data.get('from_email'),
                'attachments': email_data.get('attachments', [])
            }
            
            ticket = await self.zendesk_integration.create_ticket(ticket_data)
            logger.info(f"Initial Zendesk ticket created: {ticket['id']}")
            return ticket
            
        except Exception as e:
            logger.error(f"Error creating initial Zendesk ticket: {e}")
            raise

    async def _classify_email(self, email_data: Dict[str, Any]) -> EmailClassification:
        """Classify incoming email"""
        try:
            classification = await b.ClassifyEmail(
                subject=email_data.get('subject', ''),
                body=email_data.get('body', ''),
                hasAttachments=bool(email_data.get('attachments'))
            )
            logger.info(f"Email classified as: {classification.claimType}")
            return classification
        except Exception as e:
            logger.error(f"Error classifying email: {e}")
            # Default to general inquiry on error
            return EmailClassification(
                isClaim=False,
                claimType="general_inquiry",
                urgency="low",
                requiresImmediateResponse=False,
                suggestedResponse="Thank you for your inquiry. We will respond shortly."
            )
    
    async def _handle_general_inquiry(self, email_data: Dict[str, Any], classification: EmailClassification):
        """Handle general inquiries (non-claims)"""
        try:
            # Send appropriate response
            await self.notification_service.send_email(
                to_email=email_data.get('from_email'),
                subject="Re: " + email_data.get('subject', ''),
                message=classification.suggestedResponse
            )
            
            # Log the inquiry
            await self.database.log_inquiry(email_data, classification)
            
            logger.info("General inquiry handled successfully")
            
        except Exception as e:
            logger.error(f"Error handling general inquiry: {e}")
    
    async def _send_acknowledgment(self, claim_data: ClaimData):
        """Send immediate acknowledgment to customer with tracking link"""
        try:
            message = f"""
Dear {claim_data.email_data.get('from_name', 'Valued Customer')},

Thank you for submitting your claim. We have received your claim and assigned it the reference number: {claim_data.claim_id}

Your claim is now being processed and you will receive updates at each step of the process.

**What happens next:**
1. We will review your claim and documents
2. An expert will be assigned to your case
3. You will receive a decision within 4 hours
4. We will keep you informed throughout the process

**Track your claim status:**
{claim_data.tracking_url}

If you have any questions, please reply to this email or call our claims hotline.

Best regards,
Claims Processing Team
Zurich Insurance
            """
            
            await self.notification_service.send_email(
                to_email=claim_data.email_data.get('from_email'),
                subject=f"Claim Received - {claim_data.claim_id}",
                message=message
            )
            
            # Update claim status
            claim_data.status = "documents_processing"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
            # Update Zendesk
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment="Customer acknowledgment sent with tracking link"
            )
            
            logger.info(f"Acknowledgment sent for claim {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error sending acknowledgment: {e}")
    
    async def _process_documents(self, claim_data: ClaimData):
        """Process attached documents using OCR and update Zendesk"""
        try:
            logger.info(f"Starting document processing for claim {claim_data.claim_id}")

            attachments = claim_data.email_data.get('attachments', [])
            logger.info(f"Found {len(attachments)} attachments to process")

            if not attachments:
                logger.warning(f"No attachments found for claim {claim_data.claim_id}")
                return

            # Update Zendesk - Document processing started
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment="Document extraction started - processing attachments"
            )

            # Process each attachment
            ocr_results = []
            for i, attachment in enumerate(attachments):
                logger.info(f"Processing attachment {i+1}/{len(attachments)}")
                
                try:
                    ocr_result = await self.document_processor.process_document(attachment)
                    
                    if ocr_result:
                        ocr_results.append({
                            'filename': attachment.get('filename', 'unknown'),
                            'content': ocr_result,
                            'type': self._detect_document_type(attachment.get('filename', ''))
                        })
                        logger.info(f"OCR completed for {attachment.get('filename', 'unknown')}")
                    else:
                        logger.warning(f"Empty OCR result for {attachment.get('filename', 'unknown')}")

                except Exception as e:
                    logger.error(f"Error processing attachment {attachment.get('filename', 'unknown')}: {e}")

            logger.info(f"Document processing completed: {len(ocr_results)} documents processed")

            # Store OCR results in database
            await self.database.store_ocr_results(claim_data.claim_id, ocr_results)

            # Update claim with OCR results
            claim_data.email_data['ocr_results'] = ocr_results
            claim_data.status = "documents_processed"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)

            # Update Zendesk - Document processing completed
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment=f"Document extraction completed - {len(ocr_results)} documents processed and analyzed"
            )

            logger.info(f"Document processing completed for claim {claim_data.claim_id}")

        except Exception as e:
            logger.error(f"Error in document processing: {e}")
            # Update Zendesk with error
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment=f"Error in document processing: {str(e)}"
            )

    def _detect_document_type(self, filename: str) -> str:
        """Detect document type based on filename"""
        filename_lower = filename.lower()
        if 'police' in filename_lower or 'report' in filename_lower:
            return 'police_report'
        elif 'medical' in filename_lower or 'health' in filename_lower:
            return 'medical_record'
        elif 'receipt' in filename_lower or 'invoice' in filename_lower:
            return 'receipt'
        elif any(ext in filename_lower for ext in ['.jpg', '.jpeg', '.png', '.gif']):
            return 'photo'
        elif 'certificate' in filename_lower or 'policy' in filename_lower:
            return 'insurance_certificate'
        else:
            return 'other'
    
    async def _extract_claim_details(self, claim_data: ClaimData):
        """Extract structured claim details from email and OCR"""
        try:
            email_content = claim_data.email_data.get('body', '')
            ocr_text = '\n'.join([doc['content'] for doc in claim_data.email_data.get('ocr_results', [])])
            
            extracted_details = await b.ExtractClaimDetails(
                emailContent=email_content,
                ocrText=ocr_text
            )
            
            claim_data.extracted_details = extracted_details
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
            logger.info(f"Claim details extracted for {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error extracting claim details: {e}")
    
    async def _perform_ai_analysis(self, claim_data: ClaimData):
        """Perform comprehensive AI analysis using deep research"""
        try:
            logger.info(f"Performing AI analysis for claim {claim_data.claim_id}")
            
            # Update Zendesk - AI analysis started
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment="AI analysis started - performing deep research and assessment"
            )
            
            email_content = claim_data.email_data.get('body', '')
            attachments = [att.get('filename', '') for att in claim_data.email_data.get('attachments', [])]
            ocr_results = [doc['content'] for doc in claim_data.email_data.get('ocr_results', [])]
            
            ai_analysis = await b.AnalyzeClaim(
                emailContent=email_content,
                attachments=attachments,
                ocrResults=ocr_results
            )
            
            claim_data.ai_analysis = ai_analysis
            claim_data.status = "ai_analysis_complete"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
            # Update Zendesk with AI analysis results
            analysis_summary = self._format_ai_analysis_summary(ai_analysis)
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment=f"AI analysis completed\n\n{analysis_summary}"
            )
            
            logger.info(f"AI analysis completed for claim {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error performing AI analysis: {e}")
            # Update Zendesk with error
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment=f"Error in AI analysis: {str(e)}"
            )

    def _format_ai_analysis_summary(self, analysis: ClaimAnalysis) -> str:
        """Format AI analysis for Zendesk comment"""
        return f"""
**AI Analysis Summary**

**Classification:**
- Type: {analysis.classification.claimType}
- Severity: {analysis.classification.severity}
- Estimated Value: ${analysis.classification.estimatedValue:,.2f} CAD

**Coverage Assessment:**
- Policy Valid: {'Yes' if analysis.coverageAnalysis.policyValid else 'No'}
- Coverage: {analysis.coverageAnalysis.recommendation}
- Policy Period: {analysis.coverageAnalysis.policyPeriod}

**Fraud Analysis:**
- Risk Score: {analysis.fraudAnalysis.riskScore}/100
- Recommendation: {analysis.fraudAnalysis.recommendation}

**Liability Assessment:**
- Fault Determination: {analysis.liabilityAssessment.faultDetermination}
- Fault Percentage: {analysis.liabilityAssessment.faultPercentage}%
- Evidence Strength: {analysis.liabilityAssessment.evidenceStrength}

**Loss Quantum:**
- Total Value: ${analysis.lossQuantum.totalLossValue:,.2f} CAD
- Property Damage: ${analysis.lossQuantum.propertyDamageValue:,.2f} CAD
- Bodily Injury: ${analysis.lossQuantum.bodilyInjuryValue:,.2f} CAD

**Overall Recommendation:** {analysis.overallRecommendation}
**Priority:** {analysis.priority}
        """

    async def _send_slack_assignment_notification(self, claim_data: ClaimData):
        """Send professional Slack notification for agent assignment"""
        try:
            # Extract customer name from email or documents
            customer_name = self._extract_customer_name(claim_data)
            
            # Create professional Slack message
            slack_message = self._create_professional_slack_message(claim_data, customer_name)
            
            # Send to Slack
            await self.slack_integration.send_claim_assignment_notification(
                channel=self.config.get('slack_claims_channel', '#claims-processing'),
                message=slack_message,
                claim_data=claim_data
            )
            
            logger.info(f"Slack assignment notification sent for claim {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error sending Slack notification: {e}")

    def _extract_customer_name(self, claim_data: ClaimData) -> str:
        """Extract customer name from email or documents"""
        # Try email first
        from_name = claim_data.email_data.get('from_name', '')
        if from_name and from_name != 'Unknown':
            return from_name
        
        # Try to extract from email body
        email_body = claim_data.email_data.get('body', '')
        # Simple extraction - look for common patterns
        import re
        name_patterns = [
            r'my name is (\w+ \w+)',
            r'i am (\w+ \w+)',
            r'this is (\w+ \w+)',
            r'from (\w+ \w+)'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, email_body, re.IGNORECASE)
            if match:
                return match.group(1).title()
        
        return "Customer"

    def _create_professional_slack_message(self, claim_data: ClaimData, customer_name: str) -> str:
        """Create professional Slack message with claim details"""
        analysis = claim_data.ai_analysis
        
        # Prepare conditional values
        coverage_status = analysis.coverageAnalysis.recommendation if analysis else 'Analysis pending'
        fraud_risk = f"{analysis.fraudAnalysis.riskScore}/100" if analysis else 'Pending'
        liability_percentage = f"{analysis.liabilityAssessment.faultPercentage}% insured fault" if analysis else 'Pending'
        
        # Format the message professionally
        message = f"""
**New Claim Requires Assignment**

**Claim:** {claim_data.claim_id}
**Customer:** {customer_name} ({claim_data.email_data.get('from_email', 'N/A')})
**Type:** {claim_data.classification.claimType.replace('_', ' ').title()}
**Severity:** {claim_data.classification.severity.title()}
**Estimated Value:** ${claim_data.classification.estimatedValue:,.2f} CAD

**Quick Overview:**
• **Incident:** {claim_data.extracted_details.incidentDate or 'Date not specified'}
• **Location:** {claim_data.extracted_details.location or 'Location not specified'}
• **Documents:** {len(claim_data.email_data.get('attachments', []))} files processed
• **Coverage:** {coverage_status}
• **Fraud Risk:** {fraud_risk}
• **Liability:** {liability_percentage}

**Actions:**
[Assign to Myself] [Assign to Someone] [View Full Analysis]
        """
        
        return message

    async def _send_error_notification(self, email_data: Dict[str, Any], error_message: str):
        """Send error notification"""
        try:
            await self.notification_service.send_email(
                to_email=email_data.get('from_email'),
                subject="Claim Processing Error",
                message=f"""
Dear {email_data.get('from_name', 'Valued Customer')},

We encountered an error while processing your claim. Our technical team has been notified and will resolve this issue shortly.

Error: {error_message}

Please try submitting your claim again, or contact our support team for assistance.

Best regards,
Claims Processing Team
                """
            )
            
            # Log error for technical team
            logger.error(f"Error notification sent for email from {email_data.get('from_email')}")
            
        except Exception as e:
            logger.error(f"Error sending error notification: {e}")

    # Staged Review Methods
    async def submit_agent_review(self, claim_id: str, agent_decision: Dict[str, Any], manager_email: str):
        """Submit agent's initial review for manager approval"""
        try:
            claim_data = self.active_claims.get(claim_id)
            if not claim_data:
                claim_data = await self.database.get_claim(claim_id)
                if not claim_data:
                    raise ValueError(f"Claim {claim_id} not found")

            # Update claim with agent decision
            claim_data.agent_decision = agent_decision
            claim_data.manager_email = manager_email
            claim_data.approval_status = "pending_manager_approval"
            claim_data.status = "agent_review_complete"
            claim_data.updated_at = datetime.now()
            
            await self.database.update_claim(claim_data)

            # Update Zendesk
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment=f"Agent review submitted for manager approval\n\n**Decision:** {agent_decision.get('decision', 'N/A')}\n**Settlement Amount:** ${agent_decision.get('settlement_amount', 0):,.2f} CAD\n**Liability Split:** {agent_decision.get('liability_split', 'N/A')}\n**Reasoning:** {agent_decision.get('reasoning', 'N/A')}"
            )

            # Send manager notification
            await self._send_manager_approval_notification(claim_data, agent_decision)

            logger.info(f"Agent review submitted for claim {claim_id}")

        except Exception as e:
            logger.error(f"Error submitting agent review: {e}")
            raise

    async def _send_manager_approval_notification(self, claim_data: ClaimData, agent_decision: Dict[str, Any]):
        """Send notification to manager for approval"""
        try:
            # Email notification
            email_message = f"""
Dear Manager,

Agent has completed the initial review of claim {claim_data.claim_id} and requires your approval.

**Claim Details:**
• Customer: {claim_data.email_data.get('from_name', 'Unknown')} ({claim_data.email_data.get('from_email', 'N/A')})
• Claim Type: {claim_data.classification.claimType.replace('_', ' ').title()}
• Estimated Value: ${claim_data.classification.estimatedValue:,.2f} CAD

**Agent's Decision:**
• Decision: {agent_decision.get('decision', 'N/A')}
• Settlement Amount: ${agent_decision.get('settlement_amount', 0):,.2f} CAD
• Liability Split: {agent_decision.get('liability_split', 'N/A')}

**Agent's Reasoning:**
{agent_decision.get('reasoning', 'No reasoning provided')}

**Review Link:** {claim_data.review_url}

Please review and provide your decision within 24 hours.

Best regards,
Claims Processing System
Zurich Insurance
            """

            await self.notification_service.send_email(
                to_email=claim_data.manager_email,
                subject=f"Claim Review Required - {claim_data.claim_id}",
                message=email_message
            )

            # Slack notification
            slack_message = f"""
**Claim Review Required**

**Claim:** {claim_data.claim_id}
**Agent:** {claim_data.assigned_agent or 'Unassigned'}
**Decision:** {agent_decision.get('decision', 'N/A')}
**Value:** ${agent_decision.get('settlement_amount', 0):,.2f} CAD

**Quick Summary:**
• {claim_data.classification.claimType.replace('_', ' ').title()} claim
• {agent_decision.get('liability_split', 'Liability pending')}
• {agent_decision.get('reasoning', 'No reasoning')[:100]}...

**Actions:**
[Review Decision] [Approve] [Modify] [Reject]

*Sent to: {claim_data.manager_email}*
            """

            await self.slack_integration.send_manager_approval_notification(
                channel=self.config.get('slack_claims_channel', '#claims-processing'),
                message=slack_message,
                claim_data=claim_data
            )

            logger.info(f"Manager approval notification sent for claim {claim_data.claim_id}")

        except Exception as e:
            logger.error(f"Error sending manager approval notification: {e}")

    async def submit_manager_decision(self, claim_id: str, manager_decision: Dict[str, Any]):
        """Submit manager's decision on agent review"""
        try:
            claim_data = self.active_claims.get(claim_id)
            if not claim_data:
                claim_data = await self.database.get_claim(claim_id)
                if not claim_data:
                    raise ValueError(f"Claim {claim_id} not found")

            # Update claim with manager decision
            claim_data.manager_decision = manager_decision
            claim_data.approval_status = "manager_approved" if manager_decision.get('decision') == 'approve' else "manager_rejected"
            claim_data.status = "manager_review_complete"
            claim_data.updated_at = datetime.now()
            
            await self.database.update_claim(claim_data)

            # Update Zendesk
            decision_text = "APPROVED" if manager_decision.get('decision') == 'approve' else "REJECTED"
            modified_amount_text = f"${manager_decision.get('modified_amount', 0):,.2f} CAD" if manager_decision.get('modified_amount') else 'No changes'
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment=f"Manager decision: {decision_text}\n\n**Comments:** {manager_decision.get('comments', 'No comments')}\n**Modified Amount:** {modified_amount_text}"
            )

            # Send agent notification
            await self._send_agent_decision_notification(claim_data, manager_decision)

            logger.info(f"Manager decision submitted for claim {claim_id}")

        except Exception as e:
            logger.error(f"Error submitting manager decision: {e}")
            raise

    async def _send_agent_decision_notification(self, claim_data: ClaimData, manager_decision: Dict[str, Any]):
        """Send notification to agent about manager's decision"""
        try:
            # Get agent email from database or config
            agent_email = await self.database.get_agent_email(claim_data.assigned_agent)
            if not agent_email:
                logger.warning(f"No agent email found for {claim_data.assigned_agent}")
                return

            decision_text = "APPROVED" if manager_decision.get('decision') == 'approve' else "REJECTED"
            
            email_message = f"""
Dear {claim_data.assigned_agent},

Manager has reviewed your decision for claim {claim_data.claim_id}.

**Manager's Decision:** {decision_text}

**Manager's Comments:**
{manager_decision.get('comments', 'No comments provided')}

**Modified Settlement Amount:** {modified_amount_text}

**Next Steps:**
Please finalize this claim within 2 hours.

**Review Link:** {claim_data.review_url}

Best regards,
Claims Processing System
Zurich Insurance
            """

            await self.notification_service.send_email(
                to_email=agent_email,
                subject=f"Manager Decision - {claim_data.claim_id}",
                message=email_message
            )

            logger.info(f"Agent decision notification sent for claim {claim_data.claim_id}")

        except Exception as e:
            logger.error(f"Error sending agent decision notification: {e}")

    async def finalize_claim(self, claim_id: str, final_decision: Dict[str, Any]):
        """Finalize the claim with agent's final decision"""
        try:
            claim_data = self.active_claims.get(claim_id)
            if not claim_data:
                claim_data = await self.database.get_claim(claim_id)
                if not claim_data:
                    raise ValueError(f"Claim {claim_id} not found")

            # Update claim with final decision
            claim_data.final_decision = final_decision
            claim_data.status = "finalized"
            claim_data.updated_at = datetime.now()
            
            await self.database.update_claim(claim_data)

            # Update Zendesk
            await self.zendesk_integration.add_comment(
                ticket_id=claim_data.zendesk_ticket_id,
                comment=f"Claim finalized\n\n**Final Decision:** {final_decision.get('decision', 'N/A')}\n**Settlement Amount:** ${final_decision.get('settlement_amount', 0):,.2f} CAD\n**Liability Split:** {final_decision.get('liability_split', 'N/A')}\n**Final Comments:** {final_decision.get('comments', 'No comments')}"
            )

            # Send customer notification
            await self._send_customer_final_decision(claim_data, final_decision)

            # Send team notification
            await self._send_team_finalization_notification(claim_data, final_decision)

            logger.info(f"Claim {claim_id} finalized successfully")

        except Exception as e:
            logger.error(f"Error finalizing claim: {e}")
            raise

    async def _send_customer_final_decision(self, claim_data: ClaimData, final_decision: Dict[str, Any]):
        """Send final decision notification to customer"""
        try:
            decision_text = "APPROVED" if final_decision.get('decision') == 'approve' else "DENIED"
            
            if final_decision.get('decision') == 'approve':
                message = f"""
Dear {claim_data.email_data.get('from_name', 'Valued Customer')},

We have completed our review of your claim and reached a decision.

**DECISION: {decision_text}**

**Settlement Details:**
• Settlement Amount: ${final_decision.get('settlement_amount', 0):,.2f} CAD
• Liability Assessment: {final_decision.get('liability_split', 'N/A')}

**Next Steps:**
1. Review the settlement offer
2. Accept or request adjustments
3. Provide banking information for payment

**Track your claim:** {claim_data.tracking_url}

If you have any questions, please contact your assigned agent.

Best regards,
{claim_data.assigned_agent}
Claims Adjuster
Zurich Insurance
                """
            else:
                message = f"""
Dear {claim_data.email_data.get('from_name', 'Valued Customer')},

We have completed our review of your claim and reached a decision.

**DECISION: {decision_text}**

**Reasoning:**
{final_decision.get('reasoning', 'No reasoning provided')}

**Next Steps:**
If you disagree with this decision, you may:
1. Request a review with additional documentation
2. Contact our appeals department
3. Seek legal counsel

**Track your claim:** {claim_data.tracking_url}

If you have any questions, please contact your assigned agent.

Best regards,
{claim_data.assigned_agent}
Claims Adjuster
Zurich Insurance
                """

            await self.notification_service.send_email(
                to_email=claim_data.email_data.get('from_email'),
                subject=f"Claim Decision - {claim_data.claim_id}",
                message=message
            )

            logger.info(f"Customer final decision notification sent for claim {claim_data.claim_id}")

        except Exception as e:
            logger.error(f"Error sending customer final decision: {e}")

    async def _send_team_finalization_notification(self, claim_data: ClaimData, final_decision: Dict[str, Any]):
        """Send team notification about claim finalization"""
        try:
            decision_text = "APPROVED" if final_decision.get('decision') == 'approve' else "DENIED"
            processing_time = claim_data.updated_at - claim_data.created_at
            
            slack_message = f"""
**Claim Finalized**

**Claim:** {claim_data.claim_id}
**Customer:** {claim_data.email_data.get('from_name', 'Unknown')}
**Agent:** {claim_data.assigned_agent}
**Decision:** {decision_text}
**Settlement:** ${final_decision.get('settlement_amount', 0):,.2f} CAD

**Summary:**
• {claim_data.classification.claimType.replace('_', ' ').title()} claim completed
• Processing time: {processing_time}
• Final status: {final_decision.get('decision', 'N/A')}

**View Details:** {claim_data.review_url}
            """

            await self.slack_integration.send_team_notification(
                channel=self.config.get('slack_claims_channel', '#claims-processing'),
                message=slack_message
            )

            logger.info(f"Team finalization notification sent for claim {claim_data.claim_id}")

        except Exception as e:
            logger.error(f"Error sending team finalization notification: {e}")

    async def _monitor_active_claims(self):
        """Monitor active claims for timeouts and escalations"""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                for claim_id, claim_data in list(self.active_claims.items()):
                    # Check for timeouts
                    if current_time - claim_data.updated_at > timedelta(hours=4):
                        await self._escalate_timeout(claim_data)
                    
                    # Check for SLA violations
                    if current_time - claim_data.created_at > timedelta(hours=4):
                        await self._escalate_sla_violation(claim_data)
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in active claims monitor: {e}")
                await asyncio.sleep(60)
    
    async def _escalate_timeout(self, claim_data: ClaimData):
        """Escalate claim due to timeout"""
        try:
            logger.warning(f"Escalating claim {claim_data.claim_id} due to timeout")
            
            # Send escalation notification
            await self.slack_integration.send_escalation_notification(claim_data, "timeout")
            
            # Update status
            claim_data.status = "escalated"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
        except Exception as e:
            logger.error(f"Error escalating timeout: {e}")
    
    async def _escalate_sla_violation(self, claim_data: ClaimData):
        """Escalate claim due to SLA violation"""
        try:
            logger.warning(f"Escalating claim {claim_data.claim_id} due to SLA violation")
            
            # Send SLA violation notification
            await self.slack_integration.send_escalation_notification(claim_data, "sla_violation")
            
            # Update status
            claim_data.status = "sla_violation"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
        except Exception as e:
            logger.error(f"Error escalating SLA violation: {e}")
    
    def _dict_to_claim_data(self, claim_dict: Dict[str, Any]) -> ClaimData:
        """Convert database dictionary to ClaimData object"""
        try:
            # Create default objects for missing data
            default_classification = EmailClassification(
                isClaim=True,
                claimType=claim_dict.get('classification', {}).get('claim_type', 'general'),
                urgency=claim_dict.get('classification', {}).get('urgency', 'medium'),
                requiresImmediateResponse=False,
                suggestedResponse=""
            )

            # Create default objects with required parameters
            default_extraction = DocumentExtraction(
                policyNumber="",
                claimAmount=0.0,
                incidentDate="",
                location="",
                partiesInvolved=[],
                damages=[],
                witnesses=[],
                policeReportNumber="",
                medicalInformation=[]
            )
            default_analysis = None

            # Parse datetime strings
            created_at = datetime.fromisoformat(claim_dict['created_at'].replace('Z', '+00:00')) if isinstance(claim_dict.get('created_at'), str) else claim_dict.get('created_at', datetime.now())
            updated_at = datetime.fromisoformat(claim_dict['updated_at'].replace('Z', '+00:00')) if isinstance(claim_dict.get('updated_at'), str) else claim_dict.get('updated_at', datetime.now())

            # Extract metadata
            metadata = claim_dict.get('metadata', {})

            return ClaimData(
                claim_id=claim_dict.get('claim_number', claim_dict.get('id', 'UNKNOWN')),
                email_data=metadata.get('email_data', claim_dict.get('email_data', {})),
                classification=default_classification,
                extracted_details=default_extraction,
                ai_analysis=default_analysis,
                status=claim_dict.get('status', 'received'),
                created_at=created_at,
                updated_at=updated_at,
                zendesk_ticket_id=claim_dict.get('zendesk_ticket_id'),
                assigned_agent=claim_dict.get('assigned_agent_id'),
                decision=claim_dict.get('decision')
            )
        except Exception as e:
            logger.error(f"Error converting dict to ClaimData: {e}")
            # Return a minimal valid ClaimData object
            return ClaimData(
                claim_id=claim_dict.get('claim_number', 'ERROR'),
                email_data={},
                classification=EmailClassification(isClaim=True, claimType='general', urgency='medium', requiresImmediateResponse=False, suggestedResponse=""),
                extracted_details=DocumentExtraction(
                    policyNumber="",
                    claimAmount=0.0,
                    incidentDate="",
                    location="",
                    partiesInvolved=[],
                    damages=[],
                    witnesses=[],
                    policeReportNumber="",
                    medicalInformation=[]
                ),
                ai_analysis=None,
                status='received',
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

    async def _process_pending_claims(self):
        """Process any pending claims in the database"""
        while self.is_running:
            try:
                pending_claims_dicts = await self.database.get_pending_claims()

                for claim_dict in pending_claims_dicts:
                    claim_data = self._dict_to_claim_data(claim_dict)
                    if claim_data.claim_id not in self.active_claims:
                        self.active_claims[claim_data.claim_id] = claim_data
                        await self._determine_human_action(claim_data)

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Error processing pending claims: {e}")
                await asyncio.sleep(60)