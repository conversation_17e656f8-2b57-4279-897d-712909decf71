"""
Zendesk Integration Service
Handles ticket creation, updates, and bi-directional sync with claims workflow
"""

import asyncio
import logging
import aiohttp
import base64
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class ZendeskIntegration:
    """Integration with Zendesk for ticket management"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.subdomain = config.get('zendesk_subdomain', 'd3v-rozieai5417')
        self.email = config.get('zendesk_email', '<EMAIL>')
        self.api_token = config.get('zendesk_api_token', '1gfty1sXmithKfIhjwWnUWmTXLrxlVAJEiGrNcF1')
        self.base_url = f"https://{self.subdomain}.zendesk.com/api/v2"
        self.session = None
        
        # Create basic auth header
        credentials = f"{self.email}/token:{self.api_token}"
        self.auth_header = base64.b64encode(credentials.encode()).decode()
        
        logger.info(f"Zendesk integration initialized for {self.subdomain}")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                headers={
                    'Authorization': f'Basic {self.auth_header}',
                    'Content-Type': 'application/json'
                }
            )
        return self.session
    
    async def close(self):
        """Close the HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def create_ticket(self, ticket_data: Dict[str, Any]) -> str:
        """Create a new Zendesk ticket"""
        try:
            session = await self._get_session()
            
            # Prepare ticket data
            zendesk_ticket = {
                "ticket": {
                    "subject": ticket_data.get('subject', 'New Claim'),
                    "description": ticket_data.get('description', ''),
                    "priority": ticket_data.get('priority', 'normal'),
                    "tags": ticket_data.get('tags', []),
                    "requester": {
                        "email": ticket_data.get('requester_email', ''),
                        "name": ticket_data.get('requester_name', 'Claimant')
                    },
                    "custom_fields": [
                        {
                            "id": 360000000000,  # Custom field for claim ID
                            "value": ticket_data.get('claim_id', '')
                        },
                        {
                            "id": 360000000001,  # Custom field for claim type
                            "value": ticket_data.get('claim_type', '')
                        }
                    ]
                }
            }
            
            # Create ticket
            async with session.post(
                f"{self.base_url}/tickets.json",
                json=zendesk_ticket
            ) as response:
                if response.status == 201:
                    result = await response.json()
                    ticket_id = result['ticket']['id']
                    logger.info(f"Zendesk ticket {ticket_id} created successfully")
                    return str(ticket_id)
                else:
                    error_text = await response.text()
                    logger.error(f"Failed to create Zendesk ticket: {error_text}")
                    raise Exception(f"Zendesk ticket creation failed: {error_text}")
                    
        except Exception as e:
            logger.error(f"Error creating Zendesk ticket: {e}")
            raise
    
    async def update_ticket(self, ticket_id: str, update_data: Dict[str, Any]) -> bool:
        """Update an existing Zendesk ticket"""
        try:
            session = await self._get_session()
            
            # Prepare update data
            zendesk_update = {
                "ticket": update_data
            }
            
            # Update ticket
            async with session.put(
                f"{self.base_url}/tickets/{ticket_id}.json",
                json=zendesk_update
            ) as response:
                if response.status == 200:
                    logger.info(f"Zendesk ticket {ticket_id} updated successfully")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"Failed to update Zendesk ticket: {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error updating Zendesk ticket: {e}")
            return False
    
    async def update_ticket_status(self, ticket_id: str, status: str, comment: str = "") -> bool:
        """Update ticket status and add a comment"""
        try:
            session = await self._get_session()
            
            # Map status to Zendesk status
            status_mapping = {
                "received": "new",
                "acknowledged": "open",
                "under_review": "open",
                "pending_documents": "pending",
                "approved": "solved",
                "denied": "solved",
                "investigation": "open",
                "closed": "closed",
                "escalated": "open",
                "sla_violation": "open"
            }
            
            zendesk_status = status_mapping.get(status, "open")
            
            # Prepare update data
            update_data = {
                "status": zendesk_status
            }
            
            # Update ticket status
            success = await self.update_ticket(ticket_id, update_data)
            
            # Add comment if provided
            if success and comment:
                await self.add_comment(ticket_id, comment)
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating ticket status: {e}")
            return False
    
    async def add_comment(self, ticket_id: str, comment: str, is_public: bool = False) -> bool:
        """Add a comment to a Zendesk ticket

        Args:
            ticket_id: The Zendesk ticket ID
            comment: The comment text to add
            is_public: If False (default), comment is private and won't email the customer
                      If True, comment is public and will trigger customer email notification
        """
        try:
            session = await self._get_session()
            
            # Prepare comment data
            comment_data = {
                "ticket": {
                    "comment": {
                        "body": comment,
                        "public": is_public
                    }
                }
            }
            
            # Add comment
            async with session.put(
                f"{self.base_url}/tickets/{ticket_id}.json",
                json=comment_data
            ) as response:
                if response.status == 200:
                    logger.info(f"Comment added to Zendesk ticket {ticket_id}")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"Failed to add comment: {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error adding comment: {e}")
            return False
    
    async def assign_ticket(self, ticket_id: str, assignee_id: str) -> bool:
        """Assign a ticket to a specific agent"""
        try:
            session = await self._get_session()
            
            # Prepare assignment data
            assignment_data = {
                "ticket": {
                    "assignee_id": assignee_id
                }
            }
            
            # Assign ticket
            async with session.put(
                f"{self.base_url}/tickets/{ticket_id}.json",
                json=assignment_data
            ) as response:
                if response.status == 200:
                    logger.info(f"Ticket {ticket_id} assigned to agent {assignee_id}")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"Failed to assign ticket: {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error assigning ticket: {e}")
            return False
    
    async def add_cc_to_ticket(self, ticket_id: str, email: str) -> bool:
        """Add CC to a Zendesk ticket"""
        try:
            session = await self._get_session()
            
            # Get current ticket
            async with session.get(f"{self.base_url}/tickets/{ticket_id}.json") as response:
                if response.status != 200:
                    logger.error(f"Failed to get ticket {ticket_id}")
                    return False
                
                ticket_data = await response.json()
                current_cc = ticket_data['ticket'].get('cc_ids', [])
            
            # Add new CC
            if email not in current_cc:
                current_cc.append(email)
                
                # Update ticket with new CC
                update_data = {
                    "ticket": {
                        "cc_ids": current_cc
                    }
                }
                
                return await self.update_ticket(ticket_id, update_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding CC to ticket: {e}")
            return False
    
    async def upload_attachment(self, ticket_id: str, file_path: str, filename: str) -> str:
        """Upload an attachment to a Zendesk ticket"""
        try:
            session = await self._get_session()
            
            # First, upload the file
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Create upload session
            upload_data = {
                "filename": filename,
                "content_type": "application/octet-stream"
            }
            
            async with session.post(
                f"{self.base_url}/uploads.json",
                params={"filename": filename},
                data=file_data
            ) as response:
                if response.status == 201:
                    upload_result = await response.json()
                    token = upload_result['upload']['token']
                    
                    # Attach to ticket
                    comment_data = {
                        "ticket": {
                            "comment": {
                                "body": f"Attachment: {filename}",
                                "public": False,
                                "uploads": [token]
                            }
                        }
                    }
                    
                    async with session.put(
                        f"{self.base_url}/tickets/{ticket_id}.json",
                        json=comment_data
                    ) as response:
                        if response.status == 200:
                            logger.info(f"Attachment {filename} uploaded to ticket {ticket_id}")
                            return token
                        else:
                            logger.error(f"Failed to attach file to ticket")
                            return ""
                else:
                    logger.error(f"Failed to upload file")
                    return ""
                    
        except Exception as e:
            logger.error(f"Error uploading attachment: {e}")
            return ""
    
    async def get_ticket(self, ticket_id: str) -> Optional[Dict[str, Any]]:
        """Get ticket details"""
        try:
            session = await self._get_session()
            
            async with session.get(f"{self.base_url}/tickets/{ticket_id}.json") as response:
                if response.status == 200:
                    result = await response.json()
                    return result['ticket']
                else:
                    logger.error(f"Failed to get ticket {ticket_id}: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting ticket: {e}")
            return None
    
    async def get_ticket_comments(self, ticket_id: str) -> List[Dict[str, Any]]:
        """Get comments for a ticket"""
        try:
            session = await self._get_session()
            
            async with session.get(f"{self.base_url}/tickets/{ticket_id}/comments.json") as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('comments', [])
                else:
                    logger.error(f"Failed to get comments for ticket {ticket_id}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting ticket comments: {e}")
            return []
    
    async def search_tickets(self, query: str) -> List[Dict[str, Any]]:
        """Search for tickets"""
        try:
            session = await self._get_session()
            
            async with session.get(
                f"{self.base_url}/search.json",
                params={"query": query}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('results', [])
                else:
                    logger.error(f"Failed to search tickets: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error searching tickets: {e}")
            return []
    
    async def get_claim_tickets(self, claim_id: str) -> List[Dict[str, Any]]:
        """Get all tickets for a specific claim"""
        try:
            query = f"claim_id:{claim_id}"
            return await self.search_tickets(query)
            
        except Exception as e:
            logger.error(f"Error getting claim tickets: {e}")
            return []
    
    async def create_ticket_from_claim(self, claim_data: Dict[str, Any]) -> str:
        """Create a Zendesk ticket from claim data"""
        try:
            # Prepare ticket data
            ticket_data = {
                'subject': f"[{claim_data.get('claim_id', 'CLAIM')}] {claim_data.get('subject', 'New Claim')}",
                'description': self._format_claim_description(claim_data),
                'priority': self._map_priority(claim_data.get('priority', 'medium')),
                'tags': ['aria', 'claims', 'automated', claim_data.get('claim_type', 'liability')],
                'requester_email': claim_data.get('from_email', ''),
                'requester_name': claim_data.get('from_name', 'Claimant'),
                'claim_id': claim_data.get('claim_id', ''),
                'claim_type': claim_data.get('claim_type', 'liability')
            }
            
            # Create ticket
            ticket_id = await self.create_ticket(ticket_data)
            
            # Upload attachments if any
            if claim_data.get('attachments'):
                for attachment in claim_data['attachments']:
                    if attachment.get('temp_path'):
                        await self.upload_attachment(
                            ticket_id, 
                            attachment['temp_path'], 
                            attachment['filename']
                        )
            
            return ticket_id
            
        except Exception as e:
            logger.error(f"Error creating ticket from claim: {e}")
            raise
    
    def _format_claim_description(self, claim_data: Dict[str, Any]) -> str:
        """Format claim data for Zendesk ticket description"""
        return f"""
Claim ID: {claim_data.get('claim_id', 'N/A')}
Claim Type: {claim_data.get('claim_type', 'N/A')}
Priority: {claim_data.get('priority', 'N/A')}

Customer Information:
- Name: {claim_data.get('from_name', 'N/A')}
- Email: {claim_data.get('from_email', 'N/A')}

Claim Details:
- Subject: {claim_data.get('subject', 'N/A')}
- Received: {claim_data.get('received_at', 'N/A')}

AI Analysis:
- Severity: {claim_data.get('ai_analysis', {}).get('classification', {}).get('severity', 'N/A')}
- Estimated Value: ${claim_data.get('ai_analysis', {}).get('classification', {}).get('estimatedValue', 'N/A')}
- Fraud Risk: {claim_data.get('ai_analysis', {}).get('fraudAnalysis', {}).get('riskScore', 'N/A')}/100

Original Email Body:
{claim_data.get('body', 'N/A')}
        """
    
    def _map_priority(self, priority: str) -> str:
        """Map claim priority to Zendesk priority"""
        mapping = {
            "low": "low",
            "medium": "normal",
            "high": "high",
            "urgent": "urgent"
        }
        return mapping.get(priority, "normal")
    
    async def test_connection(self) -> bool:
        """Test Zendesk connection"""
        try:
            session = await self._get_session()
            
            async with session.get(f"{self.base_url}/users/me.json") as response:
                if response.status == 200:
                    logger.info("Zendesk connection test successful")
                    return True
                else:
                    logger.error(f"Zendesk connection test failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Zendesk connection test error: {e}")
            return False
    
    async def get_available_agents(self) -> List[Dict[str, Any]]:
        """Get list of available agents"""
        try:
            session = await self._get_session()
            
            async with session.get(f"{self.base_url}/users.json?role=agent") as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('users', [])
                else:
                    logger.error(f"Failed to get agents: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting agents: {e}")
            return [] 