"""
Web Server for Claims Liability Workflow
Serves the web interfaces and handles API endpoints
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from aiohttp import web, ClientSession
from aiohttp_cors import setup as cors_setup

logger = logging.getLogger(__name__)

class ClaimsWebServer:
    """Web server for serving claim tracking interfaces"""
    
    def __init__(self, config: Dict[str, Any], database_manager, orchestrator):
        self.config = config
        self.database = database_manager
        self.orchestrator = orchestrator
        self.app = web.Application()
        self.web_root = Path(__file__).parent.parent / 'web'
        
        # Setup CORS
        cors = cors_setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Setup routes
        self._setup_routes(cors)
        
    def _setup_routes(self, cors):
        """Setup all web routes"""
        
        # Static file serving
        self.app.router.add_static('/static', self.web_root)
        
        # Main tracking page
        self.app.router.add_get('/', self._serve_tracking_page)
        self.app.router.add_get('/track/{claim_id}', self._serve_tracking_page)
        
        # Agent review interface
        self.app.router.add_get('/review/{claim_id}', self._serve_review_page)
        
        # Manager approval interface
        self.app.router.add_get('/approve/{claim_id}', self._serve_approval_page)
        
        # API endpoints
        self.app.router.add_get('/api/claim/{claim_id}', self._get_claim_data)
        self.app.router.add_post('/api/claim/{claim_id}/agent-review', self._submit_agent_review)
        self.app.router.add_post('/api/claim/{claim_id}/manager-decision', self._submit_manager_decision)
        self.app.router.add_post('/api/claim/{claim_id}/finalize', self._finalize_claim)
        
        # Enable CORS for all routes
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    async def _serve_tracking_page(self, request):
        """Serve the main tracking page"""
        try:
            claim_id = request.match_info.get('claim_id', '')
            html_path = self.web_root / 'index.html'
            
            if html_path.exists():
                with open(html_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Inject claim ID if provided
                if claim_id:
                    content = content.replace('id="claimIdInput"', f'id="claimIdInput" value="{claim_id}"')
                
                return web.Response(
                    text=content,
                    content_type='text/html',
                    headers={'Cache-Control': 'no-cache'}
                )
            else:
                return web.Response(text="Tracking page not found", status=404)
                
        except Exception as e:
            logger.error(f"Error serving tracking page: {e}")
            return web.Response(text="Internal server error", status=500)
    
    async def _serve_review_page(self, request):
        """Serve the agent review page"""
        try:
            claim_id = request.match_info.get('claim_id', '')
            html_path = self.web_root / 'review.html'
            
            if html_path.exists():
                with open(html_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Inject claim ID
                content = content.replace('CLAIM_ID_PLACEHOLDER', claim_id)
                
                return web.Response(
                    text=content,
                    content_type='text/html',
                    headers={'Cache-Control': 'no-cache'}
                )
            else:
                return web.Response(text="Review page not found", status=404)
                
        except Exception as e:
            logger.error(f"Error serving review page: {e}")
            return web.Response(text="Internal server error", status=500)
    
    async def _serve_approval_page(self, request):
        """Serve the manager approval page"""
        try:
            claim_id = request.match_info.get('claim_id', '')
            html_path = self.web_root / 'approve.html'
            
            if html_path.exists():
                with open(html_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Inject claim ID
                content = content.replace('CLAIM_ID_PLACEHOLDER', claim_id)
                
                return web.Response(
                    text=content,
                    content_type='text/html',
                    headers={'Cache-Control': 'no-cache'}
                )
            else:
                return web.Response(text="Approval page not found", status=404)
                
        except Exception as e:
            logger.error(f"Error serving approval page: {e}")
            return web.Response(text="Internal server error", status=500)
    
    async def _get_claim_data(self, request):
        """API endpoint to get claim data"""
        try:
            claim_id = request.match_info.get('claim_id', '')
            
            # Get claim data from database
            claim_data = await self.database.get_claim(claim_id)
            
            if not claim_data:
                return web.Response(
                    text=json.dumps({'error': 'Claim not found'}),
                    content_type='application/json',
                    status=404
                )
            
            # Format response
            response_data = {
                'claim_id': claim_data.get('claim_id'),
                'customer_name': claim_data.get('customer_name'),
                'status': claim_data.get('status'),
                'created_date': claim_data.get('created_at'),
                'claim_type': claim_data.get('claim_type'),
                'incident_date': claim_data.get('incident_date'),
                'location': claim_data.get('location'),
                'policy_number': claim_data.get('policy_number'),
                'assigned_agent': claim_data.get('assigned_agent'),
                'documents_count': claim_data.get('documents_count', 0),
                'estimated_value': claim_data.get('estimated_value', 0),
                'priority': claim_data.get('priority'),
                'ai_analysis': claim_data.get('ai_analysis'),
                'timeline': await self._get_timeline_data(claim_data)
            }
            
            return web.Response(
                text=json.dumps(response_data),
                content_type='application/json'
            )
            
        except Exception as e:
            logger.error(f"Error getting claim data: {e}")
            return web.Response(
                text=json.dumps({'error': 'Internal server error'}),
                content_type='application/json',
                status=500
            )
    
    async def _submit_agent_review(self, request):
        """API endpoint to submit agent review"""
        try:
            claim_id = request.match_info.get('claim_id', '')
            data = await request.json()
            
            # Submit agent review
            await self.orchestrator.submit_agent_review(
                claim_id=claim_id,
                agent_decision=data.get('decision'),
                manager_email=data.get('manager_email')
            )
            
            return web.Response(
                text=json.dumps({'success': True, 'message': 'Agent review submitted successfully'}),
                content_type='application/json'
            )
            
        except Exception as e:
            logger.error(f"Error submitting agent review: {e}")
            return web.Response(
                text=json.dumps({'error': str(e)}),
                content_type='application/json',
                status=500
            )
    
    async def _submit_manager_decision(self, request):
        """API endpoint to submit manager decision"""
        try:
            claim_id = request.match_info.get('claim_id', '')
            data = await request.json()
            
            # Submit manager decision
            await self.orchestrator.submit_manager_decision(
                claim_id=claim_id,
                manager_decision=data.get('decision')
            )
            
            return web.Response(
                text=json.dumps({'success': True, 'message': 'Manager decision submitted successfully'}),
                content_type='application/json'
            )
            
        except Exception as e:
            logger.error(f"Error submitting manager decision: {e}")
            return web.Response(
                text=json.dumps({'error': str(e)}),
                content_type='application/json',
                status=500
            )
    
    async def _finalize_claim(self, request):
        """API endpoint to finalize claim"""
        try:
            claim_id = request.match_info.get('claim_id', '')
            data = await request.json()
            
            # Finalize claim
            await self.orchestrator.finalize_claim(
                claim_id=claim_id,
                final_decision=data.get('decision')
            )
            
            return web.Response(
                text=json.dumps({'success': True, 'message': 'Claim finalized successfully'}),
                content_type='application/json'
            )
            
        except Exception as e:
            logger.error(f"Error finalizing claim: {e}")
            return web.Response(
                text=json.dumps({'error': str(e)}),
                content_type='application/json',
                status=500
            )
    
    async def _get_timeline_data(self, claim_data: Dict[str, Any]) -> list:
        """Get timeline data for claim"""
        timeline = [
            {
                'date': claim_data.get('created_at', 'N/A'),
                'time': '2:30 PM',
                'status': 'Claim Received',
                'description': 'Email received and processed',
                'icon': '✅',
                'completed': True
            }
        ]
        
        # Add more timeline items based on status
        if claim_data.get('status') in ['documents_processed', 'ai_analysis_complete', 'under_review']:
            timeline.extend([
                {
                    'date': claim_data.get('created_at', 'N/A'),
                    'time': '2:32 PM',
                    'status': 'Document Processing',
                    'description': f"{claim_data.get('documents_count', 0)} documents processed",
                    'icon': '✅',
                    'completed': True
                },
                {
                    'date': claim_data.get('created_at', 'N/A'),
                    'time': '2:35 PM',
                    'status': 'AI Analysis Complete',
                    'description': 'Coverage and liability assessment completed',
                    'icon': '✅',
                    'completed': True
                }
            ])
        
        if claim_data.get('assigned_agent'):
            timeline.append({
                'date': claim_data.get('created_at', 'N/A'),
                'time': '2:40 PM',
                'status': 'Agent Assigned',
                'description': f"{claim_data.get('assigned_agent')} assigned to your case",
                'icon': '✅',
                'completed': True
            })
        
        if claim_data.get('status') == 'under_review':
            timeline.append({
                'date': 'Current',
                'time': 'In Progress',
                'status': 'Under Review',
                'description': 'Agent reviewing claim details',
                'icon': '🔄',
                'completed': False
            })
        
        return timeline
    
    async def start(self, host: str = '0.0.0.0', port: int = 8080):
        """Start the web server"""
        try:
            logger.info(f"Starting web server on {host}:{port}")
            runner = web.AppRunner(self.app)
            await runner.setup()
            site = web.TCPSite(runner, host, port)
            await site.start()
            logger.info(f"Web server started successfully on {host}:{port}")
            return runner
            
        except Exception as e:
            logger.error(f"Error starting web server: {e}")
            raise
    
    async def stop(self, runner):
        """Stop the web server"""
        try:
            await runner.cleanup()
            logger.info("Web server stopped")
        except Exception as e:
            logger.error(f"Error stopping web server: {e}") 