"""
Slack Integration Service
Handles team notifications and agent assignments via Slack
"""

import asyncio
import logging
import aiohttp
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class SlackIntegration:
    """Integration with Slack for team notifications"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.claims_channel = config.get('slack_claims_channel', 'C092M4E1SH0')
        self.webhook_url = config.get('slack_webhook_url', '')
        self.bot_token = config.get('slack_bot_token', '')
        self.session = None
        
        logger.info(f"Slack integration initialized for channel {self.claims_channel}")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def close(self):
        """Close the HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def send_notification(self, channel: str, message: str, requires_action: bool = False, claim_id: str = None) -> bool:
        """Send a notification to a Slack channel"""
        try:
            session = await self._get_session()

            # Prepare Slack message with fallback text
            slack_message = {
                "channel": channel,
                "text": f"New Claim Assignment Required: {claim_id}" if claim_id else "New Claim Notification",
                "blocks": [
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": message
                        }
                    }
                ]
            }

            # Add action buttons if required
            if requires_action:
                action_elements = [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "🔍 Review Claim"
                        },
                        "style": "primary",
                        "url": f"http://localhost:8080/review/{claim_id}" if claim_id else "http://localhost:8080",
                        "action_id": "review_claim"
                    },
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "👤 Assign to Me"
                        },
                        "style": "danger",
                        "action_id": f"assign_to_me_{claim_id}" if claim_id else "assign_to_me"
                    }
                ]

                slack_message["blocks"].append({
                    "type": "actions",
                    "elements": action_elements
                })
            
            # Send message
            if self.webhook_url:
                # Use webhook
                async with session.post(self.webhook_url, json=slack_message) as response:
                    if response.status == 200:
                        logger.info(f"Slack notification sent to {channel}")
                        return True
                    else:
                        logger.error(f"Failed to send Slack notification: {response.status}")
                        return False
            else:
                # Use bot token (if available)
                logger.warning("Slack webhook URL not configured, notification not sent")
                return False
                
        except Exception as e:
            logger.error(f"Error sending Slack notification: {e}")
            return False

    async def send_optimized_claim_notification(self, claim_data: Any, customer_name: str) -> bool:
        """Send an optimized Block Kit notification for new claims"""
        try:
            # Extract claim information
            claim_id = claim_data.claim_id
            claim_type = claim_data.classification.claimType.replace('_', ' ').title()
            urgency = claim_data.classification.urgency.title()
            customer_email = claim_data.email_data.get('from_email', 'N/A')

            # Calculate estimated value with better error handling
            estimated_value = "Pending analysis"
            try:
                if claim_data.ai_analysis and hasattr(claim_data.ai_analysis, 'classification'):
                    est_val = getattr(claim_data.ai_analysis.classification, 'estimatedValue', 0)
                    if est_val and est_val > 0:
                        estimated_value = f"${est_val:,.2f} CAD"
                elif claim_data.ai_analysis and hasattr(claim_data.ai_analysis, 'estimatedValue'):
                    est_val = getattr(claim_data.ai_analysis, 'estimatedValue', 0)
                    if est_val and est_val > 0:
                        estimated_value = f"${est_val:,.2f} CAD"
            except (AttributeError, TypeError, ValueError):
                estimated_value = "Analysis in progress"

            # Get analysis data with robust error handling
            analysis = claim_data.ai_analysis
            coverage_status = 'Analysis pending'
            fraud_risk = 'Pending'
            liability_percentage = 'Pending'

            try:
                if analysis:
                    if hasattr(analysis, 'coverageAnalysis') and analysis.coverageAnalysis:
                        coverage_status = getattr(analysis.coverageAnalysis, 'recommendation', 'Analysis pending')

                    if hasattr(analysis, 'fraudAnalysis') and analysis.fraudAnalysis:
                        risk_score = getattr(analysis.fraudAnalysis, 'riskScore', None)
                        if risk_score is not None:
                            fraud_risk = f"{risk_score}/100"

                    if hasattr(analysis, 'liabilityAssessment') and analysis.liabilityAssessment:
                        fault_pct = getattr(analysis.liabilityAssessment, 'faultPercentage', None)
                        if fault_pct is not None:
                            liability_percentage = f"{fault_pct}% insured fault"
            except (AttributeError, TypeError):
                # Keep default values if analysis structure is unexpected
                pass

            # Determine urgency emoji and color
            urgency_emoji = "🔴" if urgency == "High" else "🟡" if urgency == "Medium" else "🟢"

            # Create optimized Block Kit message
            blocks = [
                # Header with urgency indicator
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f"{urgency_emoji} New Claim Assignment Required"
                    }
                },
                # Main claim information
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Claim ID:*\n`{claim_id}`"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Type:*\n{claim_type}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Urgency:*\n{urgency_emoji} {urgency}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Estimated Value:*\n{estimated_value}"
                        }
                    ]
                },
                # Customer information
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Customer:* {customer_name}\n*Email:* {customer_email}"
                    }
                },
                # Divider
                {
                    "type": "divider"
                },
                # Analysis overview with context - with better data extraction
                {
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": f"📅 *Incident:* {getattr(claim_data.extracted_details, 'incidentDate', None) or 'Date not specified'}"
                        }
                    ]
                },
                {
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": f"📍 *Location:* {getattr(claim_data.extracted_details, 'location', None) or 'Location not specified'}"
                        }
                    ]
                },
                {
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": f"📎 *Documents:* {len(claim_data.email_data.get('attachments', []))} files processed"
                        }
                    ]
                },
                {
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": f"🎫 *Jira Ticket:* <https://zurich.atlassian.net/browse/{claim_id}|{claim_id}>"
                        }
                    ]
                }
            ]

            # Add risk assessment section
            risk_fields = []
            if coverage_status != 'Analysis pending':
                coverage_emoji = "✅" if "covered" in coverage_status.lower() else "❌"
                risk_fields.append({
                    "type": "mrkdwn",
                    "text": f"*Coverage:*\n{coverage_emoji} {coverage_status}"
                })

            if fraud_risk != 'Pending':
                fraud_emoji = "🚨" if int(fraud_risk.split('/')[0]) > 70 else "⚠️" if int(fraud_risk.split('/')[0]) > 30 else "✅"
                risk_fields.append({
                    "type": "mrkdwn",
                    "text": f"*Fraud Risk:*\n{fraud_emoji} {fraud_risk}"
                })

            if liability_percentage != 'Pending':
                liability_emoji = "🔴" if "80" in liability_percentage else "🟡" if "50" in liability_percentage else "🟢"
                risk_fields.append({
                    "type": "mrkdwn",
                    "text": f"*Liability:*\n{liability_emoji} {liability_percentage}"
                })

            if risk_fields:
                blocks.extend([
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": "*Risk Assessment:*"
                        },
                        "fields": risk_fields
                    }
                ])

            # Add action buttons
            blocks.extend([
                {
                    "type": "divider"
                },
                {
                    "type": "actions",
                    "elements": [
                        {
                            "type": "button",
                            "text": {
                                "type": "plain_text",
                                "text": "🔍 Review Claim",
                                "emoji": True
                            },
                            "style": "primary",
                            "url": f"http://localhost:8080/review/{claim_id}",
                            "action_id": "review_claim"
                        },
                        {
                            "type": "button",
                            "text": {
                                "type": "plain_text",
                                "text": "👤 Assign to Me",
                                "emoji": True
                            },
                            "style": "danger",
                            "action_id": f"assign_to_me_{claim_id}",
                            "confirm": {
                                "title": {
                                    "type": "plain_text",
                                    "text": "Confirm Assignment"
                                },
                                "text": {
                                    "type": "mrkdwn",
                                    "text": f"Are you sure you want to assign claim `{claim_id}` to yourself?"
                                },
                                "confirm": {
                                    "type": "plain_text",
                                    "text": "Yes, Assign"
                                },
                                "deny": {
                                    "type": "plain_text",
                                    "text": "Cancel"
                                }
                            }
                        },
                        {
                            "type": "users_select",
                            "placeholder": {
                                "type": "plain_text",
                                "text": "👥 Assign Someone Else",
                                "emoji": True
                            },
                            "action_id": f"assign_to_user_{claim_id}"
                        }
                    ]
                }
            ])

            # Add footer context with better timestamp handling
            try:
                if hasattr(claim_data, 'created_at') and claim_data.created_at:
                    timestamp = claim_data.created_at.strftime('%I:%M %p')
                else:
                    from datetime import datetime
                    timestamp = datetime.now().strftime('%I:%M %p')
            except (AttributeError, ValueError):
                timestamp = "Just now"

            blocks.append({
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"⏰ Received at {timestamp} | 🏢 Zurich Insurance Claims"
                    }
                ]
            })

            # Send the optimized message
            slack_message = {
                "channel": self.claims_channel,
                "text": f"New Claim Assignment Required: {claim_id}",
                "blocks": blocks
            }

            session = await self._get_session()

            if self.webhook_url:
                async with session.post(self.webhook_url, json=slack_message) as response:
                    if response.status == 200:
                        logger.info(f"Optimized Slack notification sent for claim {claim_id}")
                        return True
                    else:
                        logger.error(f"Failed to send optimized Slack notification: {response.status}")
                        return False
            else:
                logger.warning("Slack webhook URL not configured")
                return False

        except Exception as e:
            logger.error(f"Error sending optimized claim notification: {e}")
            return False

    async def handle_user_assignment(self, claim_id: str, selected_user_id: str, assigner_user_id: str) -> bool:
        """Handle assignment of claim to a selected user"""
        try:
            # Get user information from Slack
            user_info = await self._get_user_info(selected_user_id)
            assigner_info = await self._get_user_info(assigner_user_id)

            if not user_info or not assigner_info:
                logger.error(f"Could not get user info for assignment")
                return False

            # Send confirmation message
            confirmation_message = f"""
✅ *Claim Assignment Confirmed*

**Claim ID:** `{claim_id}`
**Assigned to:** <@{selected_user_id}> ({user_info.get('real_name', 'Unknown')})
**Assigned by:** <@{assigner_user_id}> ({assigner_info.get('real_name', 'Unknown')})

The assigned agent will receive a notification with claim details.
            """

            # Send confirmation to the channel
            await self.send_notification(
                channel=self.claims_channel,
                message=confirmation_message,
                requires_action=False
            )

            # Send direct message to assigned user
            dm_message = f"""
🔔 *New Claim Assignment*

You have been assigned claim `{claim_id}` by <@{assigner_user_id}>.

**Next Steps:**
• Review the claim details: http://localhost:8080/review/{claim_id}
• Contact the customer if additional information is needed
• Complete your review within the SLA timeframe

Please acknowledge receipt of this assignment.
            """

            await self.send_notification(
                channel=selected_user_id,  # Send DM
                message=dm_message,
                requires_action=False
            )

            logger.info(f"Claim {claim_id} assigned to user {selected_user_id} by {assigner_user_id}")
            return True

        except Exception as e:
            logger.error(f"Error handling user assignment: {e}")
            return False

    async def _get_user_info(self, user_id: str) -> dict:
        """Get user information from Slack"""
        try:
            # This would typically use the Slack Web API to get user info
            # For now, return a mock response
            return {
                'id': user_id,
                'real_name': f'User {user_id}',
                'email': f'user{user_id}@company.com'
            }
        except Exception as e:
            logger.error(f"Error getting user info: {e}")
            return {}
    
    async def send_claim_assignment(self, claim_data: Any, agent_assignment: Dict[str, Any]) -> bool:
        """Send claim assignment notification"""
        try:
            message = f"""
🚨 *New Claim Assignment*

*Claim ID:* {claim_data.claim_id}
*Agent:* {agent_assignment.get('agent_id', 'Unknown')}
*Priority:* {claim_data.ai_analysis.priority}
*Type:* {claim_data.classification.claimType}

*Customer:* {claim_data.email_data.get('from_name', 'N/A')}
*Subject:* {claim_data.email_data.get('subject', 'N/A')}

*AI Analysis:*
• Severity: {claim_data.ai_analysis.classification.severity}
• Estimated Value: ${claim_data.ai_analysis.classification.estimatedValue}
• Fraud Risk: {claim_data.ai_analysis.fraudAnalysis.riskScore}/100

*Next Steps:*
1. Review claim details
2. Analyze documents
3. Make decision
4. Update status

<https://claims.rozie.ai/agent/{claim_data.claim_id}|Review Claim>
            """
            
            return await self.send_notification(
                channel=self.claims_channel,
                message=message,
                requires_action=True
            )
            
        except Exception as e:
            logger.error(f"Error sending claim assignment: {e}")
            return False
    
    async def send_escalation_notification(self, claim_data: Any, escalation_type: str) -> bool:
        """Send escalation notification"""
        try:
            message = f"""
⚠️ *Claim Escalation Required*

*Claim ID:* {claim_data.claim_id}
*Escalation Type:* {escalation_type.replace('_', ' ').title()}
*Reason:* {'SLA violation' if escalation_type == 'sla_violation' else 'Timeout'}

*Current Status:* {claim_data.status}
*Created:* {claim_data.created_at}
*Last Updated:* {claim_data.updated_at}

*Customer:* {claim_data.email_data.get('from_name', 'N/A')}
*Priority:* {claim_data.ai_analysis.priority}

*Immediate Action Required:*
1. Review claim immediately
2. Contact customer if needed
3. Update status
4. Escalate to manager if necessary

<https://claims.rozie.ai/agent/{claim_data.claim_id}|Review Claim>
            """
            
            return await self.send_notification(
                channel=self.claims_channel,
                message=message,
                requires_action=True
            )
            
        except Exception as e:
            logger.error(f"Error sending escalation notification: {e}")
            return False
    
    async def send_decision_notification(self, claim_data: Any, decision: Dict[str, Any]) -> bool:
        """Send decision notification"""
        try:
            decision_emoji = {
                "approve": "✅",
                "deny": "❌",
                "investigate": "🔍",
                "request_more_info": "📋"
            }
            
            emoji = decision_emoji.get(decision.get('decision', ''), '📝')
            
            message = f"""
{emoji} *Claim Decision Made*

*Claim ID:* {claim_data.claim_id}
*Decision:* {decision.get('decision', 'Unknown').title()}
*Agent:* {claim_data.assigned_agent or 'Unknown'}

*Customer:* {claim_data.email_data.get('from_name', 'N/A')}
*Settlement Amount:* ${decision.get('settlement_amount', 'N/A')}

*Reasoning:* {decision.get('reasoning', 'N/A')}

*Next Steps:* {', '.join(decision.get('nextSteps', []))}

<https://claims.rozie.ai/agent/{claim_data.claim_id}|View Details>
            """
            
            return await self.send_notification(
                channel=self.claims_channel,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending decision notification: {e}")
            return False
    
    async def send_status_update(self, claim_data: Any, new_status: str, notes: str = "") -> bool:
        """Send status update notification"""
        try:
            status_emoji = {
                "received": "📥",
                "acknowledged": "✅",
                "under_review": "🔍",
                "pending_documents": "📋",
                "approved": "✅",
                "denied": "❌",
                "investigation": "🔍",
                "closed": "🔒"
            }
            
            emoji = status_emoji.get(new_status, "📝")
            
            message = f"""
{emoji} *Claim Status Update*

*Claim ID:* {claim_data.claim_id}
*New Status:* {new_status.replace('_', ' ').title()}
*Agent:* {claim_data.assigned_agent or 'Unassigned'}

*Customer:* {claim_data.email_data.get('from_name', 'N/A')}

{notes if notes else ''}

<https://claims.rozie.ai/agent/{claim_data.claim_id}|View Details>
            """
            
            return await self.send_notification(
                channel=self.claims_channel,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending status update: {e}")
            return False
    
    async def send_urgent_alert(self, claim_data: Any, alert_type: str, details: str) -> bool:
        """Send urgent alert"""
        try:
            message = f"""
🚨 *URGENT ALERT*

*Claim ID:* {claim_data.claim_id}
*Alert Type:* {alert_type}
*Priority:* {claim_data.ai_analysis.priority}

*Customer:* {claim_data.email_data.get('from_name', 'N/A')}
*Subject:* {claim_data.email_data.get('subject', 'N/A')}

*Details:* {details}

*IMMEDIATE ACTION REQUIRED*

<https://claims.rozie.ai/agent/{claim_data.claim_id}|Review Now>
            """
            
            return await self.send_notification(
                channel=self.claims_channel,
                message=message,
                requires_action=True
            )
            
        except Exception as e:
            logger.error(f"Error sending urgent alert: {e}")
            return False
    
    async def send_daily_summary(self, summary_data: Dict[str, Any]) -> bool:
        """Send daily summary"""
        try:
            message = f"""
📊 *Daily Claims Summary*

*Date:* {datetime.now().strftime('%Y-%m-%d')}

*Statistics:*
• Total Claims: {summary_data.get('total_claims', 0)}
• New Claims: {summary_data.get('new_claims', 0)}
• Resolved Claims: {summary_data.get('resolved_claims', 0)}
• Pending Claims: {summary_data.get('pending_claims', 0)}

*Performance:*
• Average Processing Time: {summary_data.get('avg_processing_time', 'N/A')}
• SLA Compliance: {summary_data.get('sla_compliance', 'N/A')}%

*Top Issues:*
{chr(10).join([f"• {issue}" for issue in summary_data.get('top_issues', [])])}

<https://claims.rozie.ai/dashboard|View Dashboard>
            """
            
            return await self.send_notification(
                channel=self.claims_channel,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending daily summary: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test Slack connection"""
        try:
            if not self.webhook_url and not self.bot_token:
                logger.warning("Slack webhook URL and bot token not configured")
                return False
            
            test_message = {
                "text": "🧪 Claims Liability Workflow System - Connection Test",
                "blocks": [
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": "✅ Slack integration test successful"
                        }
                    }
                ]
            }
            
            session = await self._get_session()
            
            if self.webhook_url:
                async with session.post(self.webhook_url, json=test_message) as response:
                    if response.status == 200:
                        logger.info("Slack connection test successful")
                        return True
                    else:
                        logger.error(f"Slack connection test failed: {response.status}")
                        return False
            else:
                logger.warning("Slack webhook URL not configured")
                return False
                
        except Exception as e:
            logger.error(f"Slack connection test error: {e}")
            return False 