"""
Notification Service
Handles email notifications to customers and agents
"""

import asyncio
import logging
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class NotificationService:
    """Handles email notifications"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.smtp_server = config.get('smtp_server', 'smtp.gmail.com')
        self.smtp_port = config.get('smtp_port', 587)
        self.sender_email = config.get('claims_email', '<EMAIL>')
        self.sender_password = config.get('claims_email_password', 'zgyqdymnzqetkvf')
        
        logger.info("Notification service initialized")
    
    async def send_email(self, to_email: str, subject: str, message: str, html_message: str = None) -> bool:
        """Send an email"""
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['From'] = self.sender_email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add text and HTML parts
            text_part = MIMEText(message, 'plain')
            msg.attach(text_part)
            
            if html_message:
                html_part = MIMEText(html_message, 'html')
                msg.attach(html_part)
            
            # Send email
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.sender_email, self.sender_password)
                server.send_message(msg)
            
            logger.info(f"Email sent to {to_email}: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email to {to_email}: {e}")
            return False
    
    async def send_claim_acknowledgment(self, claim_data: Any) -> bool:
        """Send claim acknowledgment email"""
        try:
            subject = f"Claim Received - {claim_data.claim_id}"
            
            message = f"""
Dear {claim_data.email_data.get('from_name', 'Valued Customer')},

Thank you for submitting your claim. We have received your claim and assigned it the reference number: {claim_data.claim_id}

Your claim is now being processed and you will receive updates at each step of the process.

What happens next:
1. We will review your claim and documents
2. An expert will be assigned to your case
3. You will receive a decision within 4 hours
4. We will keep you informed throughout the process

You can track your claim status using this link: {self.config.get('tracking_url', 'http://localhost:8080')}/track/{claim_data.claim_id}

If you have any questions, please reply to this email or call our claims hotline.

Best regards,
Claims Processing Team
            """
            
            html_message = f"""
<!DOCTYPE html>
<html>
<head>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; }}
        .content {{ padding: 20px 0; }}
        .footer {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 20px; }}
        .claim-id {{ font-size: 18px; font-weight: bold; color: #007bff; }}
        .steps {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Claim Received</h2>
        </div>
        
        <div class="content">
            <p>Dear {claim_data.email_data.get('from_name', 'Valued Customer')},</p>
            
            <p>Thank you for submitting your claim. We have received your claim and assigned it the reference number: <span class="claim-id">{claim_data.claim_id}</span></p>
            
            <p>Your claim is now being processed and you will receive updates at each step of the process.</p>
            
            <div class="steps">
                <h3>What happens next:</h3>
                <ol>
                    <li>We will review your claim and documents</li>
                    <li>An expert will be assigned to your case</li>
                    <li>You will receive a decision within 4 hours</li>
                    <li>We will keep you informed throughout the process</li>
                </ol>
            </div>
            
            <p>You can track your claim status using this link: <a href="{self.config.get('tracking_url', 'https://claims.rozie.ai')}/{claim_data.claim_id}">Track Your Claim</a></p>
            
            <p>If you have any questions, please reply to this email or call our claims hotline.</p>
        </div>
        
        <div class="footer">
            <p><strong>Best regards,</strong><br>
            Claims Processing Team</p>
        </div>
    </div>
</body>
</html>
            """
            
            return await self.send_email(
                to_email=claim_data.email_data.get('from_email'),
                subject=subject,
                message=message,
                html_message=html_message
            )
            
        except Exception as e:
            logger.error(f"Error sending claim acknowledgment: {e}")
            return False
    
    async def send_status_update(self, claim_data: Any, new_status: str, notes: str = "") -> bool:
        """Send status update email"""
        try:
            status_messages = {
                "under_review": "Your claim is currently under review by our expert team.",
                "pending_documents": "We need additional documents to process your claim.",
                "approved": "Your claim has been approved!",
                "denied": "Your claim has been denied.",
                "investigation": "Your claim is under investigation.",
                "closed": "Your claim has been closed."
            }
            
            subject = f"Claim Status Update - {claim_data.claim_id}"
            
            message = f"""
Dear {claim_data.email_data.get('from_name', 'Valued Customer')},

{status_messages.get(new_status, "Your claim status has been updated.")}

Status: {new_status.replace('_', ' ').title()}
Claim ID: {claim_data.claim_id}

{notes}

You can track your claim status using this link: {self.config.get('tracking_url', 'https://claims.rozie.ai')}/{claim_data.claim_id}

Best regards,
Claims Processing Team
            """
            
            return await self.send_email(
                to_email=claim_data.email_data.get('from_email'),
                subject=subject,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending status update: {e}")
            return False
    
    async def send_decision_notification(self, claim_data: Any, decision: Dict[str, Any]) -> bool:
        """Send decision notification email"""
        try:
            decision_type = decision.get('decision', 'unknown')
            
            if decision_type == "approve":
                subject = f"Claim Approved - {claim_data.claim_id}"
                status_message = "Your claim has been approved!"
                settlement_info = f"Settlement Amount: ${decision.get('settlement_amount', 'N/A')}"
            elif decision_type == "deny":
                subject = f"Claim Decision - {claim_data.claim_id}"
                status_message = "Your claim has been denied."
                settlement_info = ""
            else:
                subject = f"Claim Update - {claim_data.claim_id}"
                status_message = f"Your claim requires {decision_type.replace('_', ' ')}."
                settlement_info = ""
            
            message = f"""
Dear {claim_data.email_data.get('from_name', 'Valued Customer')},

{status_message}

Claim ID: {claim_data.claim_id}
{settlement_info}

Reasoning: {decision.get('reasoning', 'N/A')}

Next Steps: {', '.join(decision.get('nextSteps', []))}

You can track your claim status using this link: {self.config.get('tracking_url', 'https://claims.rozie.ai')}/{claim_data.claim_id}

If you have any questions, please reply to this email or call our claims hotline.

Best regards,
Claims Processing Team
            """
            
            return await self.send_email(
                to_email=claim_data.email_data.get('from_email'),
                subject=subject,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending decision notification: {e}")
            return False
    
    async def send_document_request(self, claim_data: Any, requested_documents: List[str]) -> bool:
        """Send document request email"""
        try:
            subject = f"Additional Documents Required - {claim_data.claim_id}"
            
            documents_list = "\n".join([f"• {doc}" for doc in requested_documents])
            
            message = f"""
Dear {claim_data.email_data.get('from_name', 'Valued Customer')},

To process your claim effectively, we need additional documents.

Claim ID: {claim_data.claim_id}

Required Documents:
{documents_list}

Please provide these documents by replying to this email with the attachments, or upload them using this link: {self.config.get('tracking_url', 'https://claims.rozie.ai')}/{claim_data.claim_id}

If you have any questions about the required documents, please contact us.

Best regards,
Claims Processing Team
            """
            
            return await self.send_email(
                to_email=claim_data.email_data.get('from_email'),
                subject=subject,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending document request: {e}")
            return False
    
    async def send_agent_notification(self, agent_email: str, claim_data: Any, assignment_data: Dict[str, Any]) -> bool:
        """Send agent assignment notification"""
        try:
            subject = f"New Claim Assignment - {claim_data.claim_id}"
            
            message = f"""
New claim has been assigned to you.

Claim ID: {claim_data.claim_id}
Customer: {claim_data.email_data.get('from_name', 'N/A')}
Priority: {claim_data.ai_analysis.priority}
Type: {claim_data.classification.claimType}

AI Analysis:
- Severity: {claim_data.ai_analysis.classification.severity}
- Estimated Value: ${claim_data.ai_analysis.classification.estimatedValue}
- Fraud Risk: {claim_data.ai_analysis.fraudAnalysis.riskScore}/100

Please review the claim and take appropriate action.

Review Link: {self.config.get('tracking_url', 'http://localhost:8080')}/review/{claim_data.claim_id}

Best regards,
Claims Processing System
            """
            
            return await self.send_email(
                to_email=agent_email,
                subject=subject,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending agent notification: {e}")
            return False
    
    async def send_error_notification(self, email_data: Dict[str, Any], error_message: str) -> bool:
        """Send error notification email"""
        try:
            subject = "Claim Processing Error"
            
            message = f"""
Dear {email_data.get('from_name', 'Valued Customer')},

We encountered an error while processing your claim. Our technical team has been notified and will resolve this issue shortly.

Error: {error_message}

Please try submitting your claim again, or contact our support team for assistance.

Best regards,
Claims Processing Team
            """
            
            return await self.send_email(
                to_email=email_data.get('from_email'),
                subject=subject,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending error notification: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test email connection"""
        try:
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.sender_email, self.sender_password)
            
            logger.info("Email connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Email connection test failed: {e}")
            return False 