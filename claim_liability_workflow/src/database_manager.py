"""
Database Manager Service
Handles Supabase operations for claims data
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database operations for claims data"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.supabase_url = config.get('supabase_url')
        self.supabase_anon_key = config.get('supabase_anon_key')
        self.supabase_service_role_key = config.get('supabase_service_role_key')
        self.client = None
        
        logger.info("Database manager initialized")
    
    async def initialize(self):
        """Initialize database connection"""
        try:
            # Import supabase client
            from supabase import create_client, Client
            
            # Create Supabase client
            self.client: Client = create_client(
                self.supabase_url,
                self.supabase_service_role_key
            )
            
            # Test connection
            await self.health_check()
            
            logger.info("Database connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Check database health"""
        try:
            # Simple query to test connection
            result = self.client.table('claims').select('count', count='exact').limit(1).execute()
            logger.info("Database health check passed")
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def create_claim(self, claim_data: Any) -> bool:
        """Create a new claim record"""
        try:
            # Helper function to safely serialize objects
            def safe_serialize(obj):
                if obj is None:
                    return None
                # Handle Pydantic BaseModel objects
                if hasattr(obj, 'model_dump'):
                    return json.dumps(obj.model_dump())
                elif hasattr(obj, '__dict__'):
                    return json.dumps(obj.__dict__)
                else:
                    return json.dumps(obj)

            claim_record = {
                'claim_number': claim_data.claim_id,
                'user_email': claim_data.email_data.get('from_email', '<EMAIL>'),
                'user_name': claim_data.email_data.get('from_name', 'Unknown'),
                'subject': claim_data.email_data.get('subject', 'No Subject'),
                'description': claim_data.email_data.get('body', 'No Description'),
                'status': claim_data.status,
                'zendesk_ticket_id': claim_data.zendesk_ticket_id,
                'metadata': {
                    'email_data': claim_data.email_data,
                    'classification': claim_data.classification.model_dump() if hasattr(claim_data.classification, 'model_dump') else (claim_data.classification.__dict__ if claim_data.classification else {}),
                    'extracted_details': claim_data.extracted_details.model_dump() if hasattr(claim_data.extracted_details, 'model_dump') else (claim_data.extracted_details.__dict__ if claim_data.extracted_details else {}),
                    'ai_analysis': claim_data.ai_analysis.model_dump() if hasattr(claim_data.ai_analysis, 'model_dump') else (claim_data.ai_analysis.__dict__ if claim_data.ai_analysis else {}),
                    'agent_decision': claim_data.agent_decision if isinstance(claim_data.agent_decision, dict) else {},
                    'manager_decision': claim_data.manager_decision if isinstance(claim_data.manager_decision, dict) else {},
                    'final_decision': claim_data.final_decision if isinstance(claim_data.final_decision, dict) else {}
                }
            }
            
            result = self.client.table('claims').insert(claim_record).execute()
            
            if result.data:
                logger.info(f"Claim {claim_data.claim_id} created in database")
                return True
            else:
                logger.error(f"Failed to create claim {claim_data.claim_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error creating claim: {e}")
            return False
    
    async def update_claim(self, claim_data: Any) -> bool:
        """Update an existing claim record"""
        try:
            # Helper function to safely serialize objects
            def safe_serialize(obj):
                if obj is None:
                    return None
                # Handle Pydantic BaseModel objects
                if hasattr(obj, 'model_dump'):
                    return json.dumps(obj.model_dump())
                elif hasattr(obj, '__dict__'):
                    return json.dumps(obj.__dict__)
                else:
                    return json.dumps(obj)

            update_data = {
                'user_email': claim_data.email_data.get('from_email', '<EMAIL>'),
                'user_name': claim_data.email_data.get('from_name', 'Unknown'),
                'subject': claim_data.email_data.get('subject', 'No Subject'),
                'description': claim_data.email_data.get('body', 'No Description'),
                'status': claim_data.status,
                'zendesk_ticket_id': claim_data.zendesk_ticket_id,
                'metadata': {
                    'email_data': claim_data.email_data,
                    'classification': claim_data.classification.model_dump() if hasattr(claim_data.classification, 'model_dump') else (claim_data.classification.__dict__ if claim_data.classification else {}),
                    'extracted_details': claim_data.extracted_details.model_dump() if hasattr(claim_data.extracted_details, 'model_dump') else (claim_data.extracted_details.__dict__ if claim_data.extracted_details else {}),
                    'ai_analysis': claim_data.ai_analysis.model_dump() if hasattr(claim_data.ai_analysis, 'model_dump') else (claim_data.ai_analysis.__dict__ if claim_data.ai_analysis else {}),
                    'agent_decision': claim_data.agent_decision if isinstance(claim_data.agent_decision, dict) else {},
                    'manager_decision': claim_data.manager_decision if isinstance(claim_data.manager_decision, dict) else {},
                    'final_decision': claim_data.final_decision if isinstance(claim_data.final_decision, dict) else {}
                }
            }

            result = self.client.table('claims').update(update_data).eq('claim_number', claim_data.claim_id).execute()
            
            if result.data:
                logger.info(f"Claim {claim_data.claim_id} updated in database")
                return True
            else:
                logger.error(f"Failed to update claim {claim_data.claim_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating claim: {e}")
            return False
    
    async def get_claim(self, claim_id: str) -> Optional[Dict[str, Any]]:
        """Get a claim by ID"""
        try:
            result = self.client.table('claims').select('*').eq('claim_number', claim_id).execute()
            
            if result.data:
                claim_data = result.data[0]
                # Parse JSON fields
                claim_data['email_data'] = json.loads(claim_data['email_data'])
                claim_data['classification'] = json.loads(claim_data['classification'])
                claim_data['extracted_details'] = json.loads(claim_data['extracted_details'])
                claim_data['ai_analysis'] = json.loads(claim_data['ai_analysis'])
                if claim_data.get('agent_decision'):
                    claim_data['agent_decision'] = json.loads(claim_data['agent_decision'])
                if claim_data.get('manager_decision'):
                    claim_data['manager_decision'] = json.loads(claim_data['manager_decision'])
                if claim_data.get('final_decision'):
                    claim_data['final_decision'] = json.loads(claim_data['final_decision'])
                
                return claim_data
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error getting claim: {e}")
            return None
    
    async def get_all_claims(self) -> List[Dict[str, Any]]:
        """Get all claims"""
        try:
            result = self.client.table('claims').select('*').order('created_at', desc=True).execute()
            
            claims = []
            for claim_data in result.data:
                # Parse JSON fields
                claim_data['email_data'] = json.loads(claim_data['email_data'])
                claim_data['classification'] = json.loads(claim_data['classification'])
                claim_data['extracted_details'] = json.loads(claim_data['extracted_details'])
                claim_data['ai_analysis'] = json.loads(claim_data['ai_analysis'])
                if claim_data.get('agent_decision'):
                    claim_data['agent_decision'] = json.loads(claim_data['agent_decision'])
                if claim_data.get('manager_decision'):
                    claim_data['manager_decision'] = json.loads(claim_data['manager_decision'])
                if claim_data.get('final_decision'):
                    claim_data['final_decision'] = json.loads(claim_data['final_decision'])
                
                claims.append(claim_data)
            
            return claims
            
        except Exception as e:
            logger.error(f"Error getting all claims: {e}")
            return []
    
    async def get_pending_claims(self) -> List[Dict[str, Any]]:
        """Get pending claims"""
        try:
            pending_statuses = ['received', 'documents_processing', 'ai_analysis', 'human_review', 'pending_approval']
            result = self.client.table('claims').select('*').in_('status', pending_statuses).order('created_at', desc=True).execute()
            
            claims = []
            for claim_data in result.data:
                # Parse JSON fields only if they are strings
                if isinstance(claim_data.get('email_data'), str):
                    claim_data['email_data'] = json.loads(claim_data['email_data'])
                if isinstance(claim_data.get('classification'), str):
                    claim_data['classification'] = json.loads(claim_data['classification'])
                if isinstance(claim_data.get('extracted_details'), str):
                    claim_data['extracted_details'] = json.loads(claim_data['extracted_details'])
                if isinstance(claim_data.get('ai_analysis'), str):
                    claim_data['ai_analysis'] = json.loads(claim_data['ai_analysis'])
                if claim_data.get('agent_decision') and isinstance(claim_data['agent_decision'], str):
                    claim_data['agent_decision'] = json.loads(claim_data['agent_decision'])
                if claim_data.get('manager_decision') and isinstance(claim_data['manager_decision'], str):
                    claim_data['manager_decision'] = json.loads(claim_data['manager_decision'])
                if claim_data.get('final_decision') and isinstance(claim_data['final_decision'], str):
                    claim_data['final_decision'] = json.loads(claim_data['final_decision'])

                claims.append(claim_data)
            
            return claims
            
        except Exception as e:
            logger.error(f"Error getting pending claims: {e}")
            return []
    
    async def store_ocr_results(self, claim_id: str, ocr_results: List[str]) -> bool:
        """Store OCR results for a claim"""
        try:
            ocr_record = {
                'claim_id': claim_id,
                'ocr_results': json.dumps(ocr_results),
                'created_at': datetime.now().isoformat()
            }
            
            result = self.client.table('ocr_results').insert(ocr_record).execute()
            
            if result.data:
                logger.info(f"OCR results stored for claim {claim_id}")
                return True
            else:
                logger.error(f"Failed to store OCR results for claim {claim_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error storing OCR results: {e}")
            return False
    
    async def log_inquiry(self, email_data: Dict[str, Any], classification: Any) -> bool:
        """Log general inquiry"""
        try:
            # Helper function to safely serialize objects
            def safe_serialize(obj):
                if obj is None:
                    return None
                # Handle Pydantic BaseModel objects
                if hasattr(obj, 'model_dump'):
                    return json.dumps(obj.model_dump())
                elif hasattr(obj, '__dict__'):
                    return json.dumps(obj.__dict__)
                else:
                    return json.dumps(obj)

            inquiry_record = {
                'email_data': json.dumps(email_data),
                'classification': safe_serialize(classification),
                'created_at': datetime.now().isoformat()
            }
            
            result = self.client.table('inquiries').insert(inquiry_record).execute()
            
            if result.data:
                logger.info("Inquiry logged successfully")
                return True
            else:
                logger.error("Failed to log inquiry")
                return False
                
        except Exception as e:
            logger.error(f"Error logging inquiry: {e}")
            return False
    
    async def get_available_agents(self) -> List[str]:
        """Get list of available agents"""
        try:
            # This would typically query an agents table
            # For now, return a default list
            return [
                "agent_001",
                "agent_002", 
                "agent_003",
                "agent_004",
                "agent_005"
            ]
            
        except Exception as e:
            logger.error(f"Error getting available agents: {e}")
            return []
    
    async def log_audit_event(self, claim_id: str, event_type: str, event_data: Dict[str, Any]) -> bool:
        """Log audit event"""
        try:
            audit_record = {
                'claim_id': claim_id,
                'event_type': event_type,
                'event_data': json.dumps(event_data),
                'timestamp': datetime.now().isoformat()
            }
            
            result = self.client.table('audit_logs').insert(audit_record).execute()
            
            if result.data:
                logger.info(f"Audit event logged: {event_type} for claim {claim_id}")
                return True
            else:
                logger.error(f"Failed to log audit event for claim {claim_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error logging audit event: {e}")
            return False
    
    async def get_claim_statistics(self) -> Dict[str, Any]:
        """Get claim statistics"""
        try:
            # Get total claims
            total_result = self.client.table('claims').select('count', count='exact').execute()
            total_claims = total_result.count if total_result.count else 0
            
            # Get claims by status
            status_result = self.client.table('claims').select('status').execute()
            status_counts = {}
            for claim in status_result.data:
                status = claim['status']
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # Get recent claims (last 24 hours)
            yesterday = (datetime.now() - timedelta(days=1)).isoformat()
            recent_result = self.client.table('claims').select('count', count='exact').gte('created_at', yesterday).execute()
            recent_claims = recent_result.count if recent_result.count else 0
            
            return {
                'total_claims': total_claims,
                'status_counts': status_counts,
                'recent_claims': recent_claims
            }
            
        except Exception as e:
            logger.error(f"Error getting claim statistics: {e}")
            return {
                'total_claims': 0,
                'status_counts': {},
                'recent_claims': 0
            }
    
    async def close(self):
        """Close database connection"""
        try:
            if self.client:
                # Supabase client doesn't have a close method
                self.client = None
            logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {e}") 