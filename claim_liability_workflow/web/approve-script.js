// Manager Approval Application
class ManagerApproval {
    constructor() {
        this.claimData = null;
        this.agentDecision = null;
        this.currentDecision = {
            type: 'approve',
            comments: '',
            modifiedAmount: null,
            modifiedLiability: null,
            rejectionReason: '',
            nextSteps: ''
        };
        this.initializeEventListeners();
        this.loadApprovalData();
    }

    initializeEventListeners() {
        // Decision buttons
        document.getElementById('approveBtn')?.addEventListener('click', () => this.setDecisionType('approve'));
        document.getElementById('modifyBtn')?.addEventListener('click', () => this.setDecisionType('modify'));
        document.getElementById('rejectBtn')?.addEventListener('click', () => this.setDecisionType('reject'));

        // Modification controls
        const newLiabilitySlider = document.getElementById('newLiabilitySlider');
        if (newLiabilitySlider) {
            newLiabilitySlider.addEventListener('input', (e) => this.updateNewLiability(e.target.value));
        }

        const newAmount = document.getElementById('newAmount');
        if (newAmount) {
            newAmount.addEventListener('input', (e) => this.updateNewAmount(e.target.value));
        }

        const modificationReason = document.getElementById('modificationReason');
        if (modificationReason) {
            modificationReason.addEventListener('input', (e) => this.updateModificationReason(e.target.value));
        }

        // Rejection controls
        const rejectionReason = document.getElementById('rejectionReason');
        if (rejectionReason) {
            rejectionReason.addEventListener('input', (e) => this.updateRejectionReason(e.target.value));
        }

        const nextSteps = document.getElementById('nextSteps');
        if (nextSteps) {
            nextSteps.addEventListener('change', (e) => this.updateNextSteps(e.target.value));
        }

        // Manager comments
        const managerComments = document.getElementById('managerComments');
        if (managerComments) {
            managerComments.addEventListener('input', (e) => this.updateManagerComments(e.target.value));
        }

        // Submit decision
        document.getElementById('submitDecisionBtn')?.addEventListener('click', () => this.submitDecision());

        // Close button
        document.getElementById('closeBtn')?.addEventListener('click', () => this.closeApproval());

        // Modal events
        document.getElementById('modalCancel')?.addEventListener('click', () => this.hideModal());
        document.getElementById('modalConfirm')?.addEventListener('click', () => this.confirmDecision());
        document.querySelector('.modal-close')?.addEventListener('click', () => this.hideModal());

        // Quick action buttons
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleQuickAction(e.target.closest('.action-btn').dataset.action));
        });
    }

    async loadApprovalData() {
        // Get claim ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const claimId = urlParams.get('claim');
        
        if (!claimId) {
            this.showError('No claim ID provided');
            return;
        }

        try {
            // In production, this would be an actual API call
            const approvalData = await this.fetchApprovalData(claimId);
            this.claimData = approvalData.claim;
            this.agentDecision = approvalData.agentDecision;
            this.displayApprovalData();
            this.loadDocuments();
        } catch (error) {
            console.error('Error loading approval data:', error);
            this.showError('Failed to load approval data');
        }
    }

    async fetchApprovalData(claimId) {
        // Simulate API call with mock data
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    claim: {
                        claim_id: claimId,
                        customer_name: 'John Smith',
                        claim_type: 'Auto Liability',
                        agent_name: 'Sarah Johnson',
                        submitted_time: '2 hours ago',
                        priority: 'High',
                        total_value: 15000,
                        ai_analysis: {
                            coverage: 'Covered',
                            fraud_risk: 'Low (15/100)',
                            liability: 'Clear (60% insured)',
                            recommendation: 'Approve with conditions'
                        }
                    },
                    agentDecision: {
                        decision: 'approve',
                        settlement_amount: 9000,
                        liability_split: '60% insured fault',
                        processing_time: '45 minutes',
                        confidence_level: 'High (85%)',
                        reasoning: 'Based on the police report and witness statements, the insured driver was clearly at fault for the collision. The damage assessment shows $8,000 in property damage and $7,000 in medical expenses. Given the clear liability and supporting documentation, I recommend approving the claim with a 60% liability split, resulting in a $9,000 settlement.'
                    }
                });
            }, 1000);
        });
    }

    displayApprovalData() {
        if (!this.claimData || !this.agentDecision) return;

        // Update claim summary
        document.getElementById('claimId').textContent = this.claimData.claim_id;
        document.getElementById('customerName').textContent = this.claimData.customer_name;
        document.getElementById('claimType').textContent = this.claimData.claim_type;
        document.getElementById('agentName').textContent = this.claimData.agent_name;
        document.getElementById('submittedTime').textContent = this.claimData.submitted_time;
        document.getElementById('priority').textContent = this.claimData.priority;

        // Update agent decision
        document.getElementById('agentDecision').textContent = this.agentDecision.decision.charAt(0).toUpperCase() + this.agentDecision.decision.slice(1);
        document.getElementById('settlementAmount').textContent = `$${this.agentDecision.settlement_amount.toLocaleString('en-CA')} CAD`;
        document.getElementById('liabilitySplit').textContent = this.agentDecision.liability_split;
        document.getElementById('processingTime').textContent = this.agentDecision.processing_time;
        document.getElementById('confidenceLevel').textContent = this.agentDecision.confidence_level;

        // Update agent reasoning
        document.getElementById('agentReasoning').textContent = this.agentDecision.reasoning;

        // Update AI analysis
        const analysis = this.claimData.ai_analysis;
        document.getElementById('coverageStatus').textContent = analysis.coverage;
        document.getElementById('fraudRisk').textContent = analysis.fraud_risk;
        document.getElementById('liabilityAssessment').textContent = analysis.liability;
        document.getElementById('aiRecommendation').textContent = analysis.recommendation;

        // Set initial values for modification
        document.getElementById('newLiabilitySlider').value = 60;
        document.getElementById('newLiabilityValue').textContent = '60%';
        document.getElementById('newAmount').value = this.agentDecision.settlement_amount;
    }

    loadDocuments() {
        const documentsList = document.getElementById('documentsList');
        const mockDocuments = [
            {
                title: 'Police Report',
                type: 'pdf',
                size: '2.3 MB',
                uploaded: '2025-01-15 14:30'
            },
            {
                title: 'Medical Records',
                type: 'pdf',
                size: '1.8 MB',
                uploaded: '2025-01-15 14:32'
            },
            {
                title: 'Witness Statement',
                type: 'docx',
                size: '0.5 MB',
                uploaded: '2025-01-15 14:35'
            },
            {
                title: 'Damage Assessment',
                type: 'pdf',
                size: '1.2 MB',
                uploaded: '2025-01-15 14:38'
            }
        ];

        documentsList.innerHTML = '';
        mockDocuments.forEach(doc => {
            const docItem = document.createElement('div');
            docItem.className = 'document-item';
            docItem.innerHTML = `
                <div class="document-icon">
                    <i class="fas fa-${this.getDocumentIcon(doc.type)}"></i>
                </div>
                <div class="document-info">
                    <div class="document-title">${doc.title}</div>
                    <div class="document-meta">${doc.size} • ${doc.uploaded}</div>
                </div>
            `;
            docItem.addEventListener('click', () => this.viewDocument(doc));
            documentsList.appendChild(docItem);
        });
    }

    getDocumentIcon(type) {
        switch (type) {
            case 'pdf': return 'file-pdf';
            case 'docx': return 'file-word';
            case 'xlsx': return 'file-excel';
            case 'jpg':
            case 'png': return 'file-image';
            default: return 'file';
        }
    }

    setDecisionType(type) {
        this.currentDecision.type = type;
        
        // Update button states
        document.querySelectorAll('.decision-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeBtn = document.querySelector(`#${type}Btn`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Show/hide panels
        document.getElementById('modificationPanel').classList.add('hidden');
        document.getElementById('rejectionPanel').classList.add('hidden');

        if (type === 'modify') {
            document.getElementById('modificationPanel').classList.remove('hidden');
        } else if (type === 'reject') {
            document.getElementById('rejectionPanel').classList.remove('hidden');
        }
    }

    updateNewLiability(value) {
        this.currentDecision.modifiedLiability = parseInt(value);
        document.getElementById('newLiabilityValue').textContent = `${value}%`;
        
        // Update amount based on new liability
        const newAmount = Math.round(this.claimData.total_value * (value / 100));
        document.getElementById('newAmount').value = newAmount;
        this.currentDecision.modifiedAmount = newAmount;
    }

    updateNewAmount(value) {
        this.currentDecision.modifiedAmount = parseFloat(value) || 0;
    }

    updateModificationReason(value) {
        this.currentDecision.modificationReason = value;
    }

    updateRejectionReason(value) {
        this.currentDecision.rejectionReason = value;
    }

    updateNextSteps(value) {
        this.currentDecision.nextSteps = value;
    }

    updateManagerComments(value) {
        this.currentDecision.comments = value;
    }

    async submitDecision() {
        if (!this.validateDecision()) {
            return;
        }

        this.showConfirmationModal();
    }

    validateDecision() {
        if (this.currentDecision.type === 'modify') {
            if (!this.currentDecision.modificationReason?.trim()) {
                this.showError('Please provide a reason for modification');
                return false;
            }
        } else if (this.currentDecision.type === 'reject') {
            if (!this.currentDecision.rejectionReason?.trim()) {
                this.showError('Please provide a reason for rejection');
                return false;
            }
        }

        return true;
    }

    showConfirmationModal() {
        const modal = document.getElementById('confirmationModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const decisionSummary = document.getElementById('decisionSummary');

        modalTitle.textContent = 'Confirm Manager Decision';
        modalMessage.textContent = `Are you sure you want to ${this.currentDecision.type} this agent's decision?`;

        // Create decision summary
        let summaryHTML = `<strong>Decision:</strong> ${this.currentDecision.type.toUpperCase()}<br>`;
        
        if (this.currentDecision.type === 'approve') {
            summaryHTML += `<strong>Comments:</strong> ${this.currentDecision.comments || 'No additional comments'}`;
        } else if (this.currentDecision.type === 'modify') {
            summaryHTML += `<strong>New Amount:</strong> $${this.currentDecision.modifiedAmount?.toLocaleString('en-CA') || this.agentDecision.settlement_amount.toLocaleString('en-CA')} CAD<br>`;
            summaryHTML += `<strong>New Liability:</strong> ${this.currentDecision.modifiedLiability || 60}%<br>`;
            summaryHTML += `<strong>Reason:</strong> ${this.currentDecision.modificationReason}`;
        } else if (this.currentDecision.type === 'reject') {
            summaryHTML += `<strong>Reason:</strong> ${this.currentDecision.rejectionReason}<br>`;
            summaryHTML += `<strong>Next Steps:</strong> ${this.currentDecision.nextSteps}`;
        }

        decisionSummary.innerHTML = summaryHTML;
        modal.classList.remove('hidden');
    }

    hideModal() {
        document.getElementById('confirmationModal').classList.add('hidden');
    }

    async confirmDecision() {
        this.hideModal();
        
        try {
            // Show loading state
            const submitBtn = document.getElementById('submitDecisionBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
            submitBtn.disabled = true;

            // Simulate API call
            await this.submitDecisionToAPI();

            // Show success message
            this.showSuccess('Decision submitted successfully! Agent will be notified.');

            // Close the approval window after a delay
            setTimeout(() => {
                window.close();
            }, 2000);

        } catch (error) {
            console.error('Error submitting decision:', error);
            this.showError('Failed to submit decision. Please try again.');
        } finally {
            // Restore button
            const submitBtn = document.getElementById('submitDecisionBtn');
            submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Submit Decision';
            submitBtn.disabled = false;
        }
    }

    async submitDecisionToAPI() {
        // Simulate API call
        return new Promise((resolve) => {
            setTimeout(() => {
                console.log('Manager decision submitted:', this.currentDecision);
                resolve();
            }, 2000);
        });
    }

    handleQuickAction(action) {
        switch (action) {
            case 'view-full-claim':
                this.viewFullClaim();
                break;
            case 'contact-agent':
                this.contactAgent();
                break;
            case 'request-info':
                this.requestInfo();
                break;
            case 'escalate':
                this.escalate();
                break;
        }
    }

    viewFullClaim() {
        // In production, this would open the full claim view
        console.log('Viewing full claim:', this.claimData.claim_id);
        this.showModal('View Full Claim', 'Opening full claim details...');
    }

    contactAgent() {
        // In production, this would initiate contact with the agent
        console.log('Contacting agent:', this.claimData.agent_name);
        this.showModal('Contact Agent', 'Initiating contact with agent...');
    }

    requestInfo() {
        // In production, this would request additional information
        console.log('Requesting additional information');
        this.showModal('Request Information', 'What additional information do you need?');
    }

    escalate() {
        // In production, this would escalate the claim
        console.log('Escalating claim:', this.claimData.claim_id);
        this.showModal('Escalate Claim', 'Escalating claim to senior management...');
    }

    showModal(title, message) {
        const modal = document.getElementById('confirmationModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');

        modalTitle.textContent = title;
        modalMessage.textContent = message;

        modal.classList.remove('hidden');
    }

    viewDocument(doc) {
        // In production, this would open the document viewer
        console.log('Viewing document:', doc);
        this.showModal('Document Viewer', `Opening ${doc.title}...`);
    }

    closeApproval() {
        if (this.currentDecision.comments?.trim()) {
            if (confirm('You have unsaved comments. Are you sure you want to close?')) {
                window.close();
            }
        } else {
            window.close();
        }
    }

    showSuccess(message) {
        // Create success notification
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;
        this.showNotification(notification);
    }

    showError(message) {
        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'notification error';
        notification.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        `;
        this.showNotification(notification);
    }

    showNotification(notification) {
        // Add notification styles if not already present
        if (!document.getElementById('notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'notification-styles';
            styles.textContent = `
                .notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 1rem 1.5rem;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    z-index: 1001;
                    animation: slideIn 0.3s ease;
                }
                .notification.success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                }
                .notification.error {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                }
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }

        document.body.appendChild(notification);

        // Remove notification after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ManagerApproval();
});

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + Enter to submit
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        document.getElementById('submitDecisionBtn').click();
    }
    
    // Escape to close modal
    if (e.key === 'Escape') {
        const modal = document.getElementById('confirmationModal');
        if (!modal.classList.contains('hidden')) {
            modal.classList.add('hidden');
        }
    }
    
    // Number keys for quick decisions
    if (e.key === '1') {
        document.getElementById('approveBtn').click();
    } else if (e.key === '2') {
        document.getElementById('modifyBtn').click();
    } else if (e.key === '3') {
        document.getElementById('rejectBtn').click();
    }
});

// Add active button styles
const activeButtonStyles = `
    .decision-btn.active {
        transform: scale(1.02);
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = activeButtonStyles;
document.head.appendChild(styleSheet); 