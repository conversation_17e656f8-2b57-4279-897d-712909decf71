/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8fafc;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #1f4e79 0%, #2980b9 100%);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 40px;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-danger, .btn-warning, .btn-close {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-secondary {
    background: #64748b;
    color: white;
}

.btn-secondary:hover {
    background: #475569;
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-close {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
    width: 100%;
    justify-content: center;
}

/* Main Content */
.main {
    padding: 2rem 0;
}

/* Claim Header */
.claim-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.claim-id {
    font-size: 2rem;
    font-weight: 700;
    color: #1f4e79;
    margin-bottom: 0.5rem;
}

.claim-meta {
    display: flex;
    gap: 2rem;
    color: #64748b;
}

.claim-meta strong {
    color: #1f4e79;
}

.claim-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

/* Left Column */
.left-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Section Titles */
.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: #3b82f6;
}

/* Analysis Section */
.analysis-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.analysis-card {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.analysis-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.analysis-header {
    background: #f8fafc;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.analysis-header i {
    color: #3b82f6;
    font-size: 1.2rem;
}

.analysis-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f4e79;
}

.analysis-content {
    padding: 1rem;
}

.analysis-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.analysis-item:last-child {
    border-bottom: none;
}

.analysis-item .label {
    font-weight: 500;
    color: #64748b;
}

.analysis-item .value {
    font-weight: 600;
    color: #1f4e79;
}

.value.success {
    color: #10b981;
}

.value.low-risk {
    color: #10b981;
}

.value.medium-risk {
    color: #f59e0b;
}

.value.high-risk {
    color: #ef4444;
}

/* Documents Section */
.documents-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.document-card {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.document-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.document-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.document-icon {
    width: 40px;
    height: 40px;
    background: #f1f5f9;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
}

.document-title {
    font-weight: 600;
    color: #1f4e79;
    font-size: 0.9rem;
}

.document-meta {
    color: #64748b;
    font-size: 0.8rem;
}

/* Right Column */
.right-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Decision Panel */
.decision-panel {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.decision-section {
    margin-bottom: 2rem;
}

.decision-section:last-child {
    margin-bottom: 0;
}

.decision-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1rem;
}

/* Liability Slider */
.liability-slider {
    margin-bottom: 1rem;
}

.liability-slider label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #64748b;
}

.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: none;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    color: #64748b;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

/* Amount Input */
.amount-input {
    position: relative;
    margin-bottom: 0.5rem;
}

.amount-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #64748b;
}

.amount-field {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.amount-field:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.currency {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-weight: 500;
}

.amount-note {
    color: #64748b;
    font-size: 0.8rem;
    font-style: italic;
}

/* Reasoning Textarea */
.reasoning-textarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
    outline: none;
    transition: border-color 0.3s ease;
}

.reasoning-textarea:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Manager Input */
.manager-input {
    margin-bottom: 0.5rem;
}

.manager-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #64748b;
}

.email-field {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.email-field:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.approval-note {
    color: #64748b;
    font-size: 0.8rem;
    font-style: italic;
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.quick-actions h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1rem;
}

.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.action-btn {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
}

.action-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.action-btn i {
    font-size: 1.2rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #1f4e79;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #64748b;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.modal-close:hover {
    background: #f1f5f9;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body p {
    color: #64748b;
    line-height: 1.6;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .analysis-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .claim-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .claim-actions {
        width: 100%;
        justify-content: center;
    }
    
    .claim-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .modal-footer button {
        width: 100%;
    }
} 