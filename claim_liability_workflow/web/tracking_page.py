import streamlit as st
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
import json

class ClaimTrackingPage:
    """Professional claim tracking interface for customers"""
    
    def __init__(self, database_manager, config: Dict[str, Any]):
        self.database = database_manager
        self.config = config
        
    def render_tracking_page(self, claim_id: str):
        """Render the main tracking page"""
        st.set_page_config(
            page_title=f"Claim Tracking - {claim_id}",
            page_icon="📋",
            layout="wide"
        )
        
        # Header
        st.markdown("""
        <div style="background: linear-gradient(90deg, #1f4e79 0%, #2980b9 100%); padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
            <h1 style="color: white; margin: 0; font-size: 2.5rem;">Zurich Insurance</h1>
            <p style="color: #e8f4fd; margin: 0; font-size: 1.2rem;">Claim Tracking System</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Get claim data
        claim_data = self._get_claim_data(claim_id)
        
        if not claim_data:
            st.error("Claim not found. Please check your claim ID.")
            return
        
        # Claim header
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            st.markdown(f"""
            <h2 style="color: #1f4e79; margin-bottom: 0.5rem;">Claim: {claim_id}</h2>
            <p style="color: #666; font-size: 1.1rem;">Customer: {claim_data.get('customer_name', 'N/A')}</p>
            """, unsafe_allow_html=True)
        
        with col2:
            st.metric(
                label="Status",
                value=claim_data.get('status', 'Unknown').replace('_', ' ').title()
            )
        
        with col3:
            st.metric(
                label="Created",
                value=claim_data.get('created_date', 'N/A')
            )
        
        # Timeline
        st.markdown("---")
        st.markdown("## Claim Timeline")
        
        timeline_data = self._get_timeline_data(claim_data)
        self._render_timeline(timeline_data)
        
        # Claim details
        st.markdown("---")
        st.markdown("## Claim Details")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("### Basic Information")
            st.write(f"**Claim Type:** {claim_data.get('claim_type', 'N/A')}")
            st.write(f"**Incident Date:** {claim_data.get('incident_date', 'N/A')}")
            st.write(f"**Location:** {claim_data.get('location', 'N/A')}")
            st.write(f"**Policy Number:** {claim_data.get('policy_number', 'N/A')}")
        
        with col2:
            st.markdown("### Processing Information")
            st.write(f"**Assigned Agent:** {claim_data.get('assigned_agent', 'Not assigned yet')}")
            st.write(f"**Documents Processed:** {claim_data.get('documents_count', 0)}")
            st.write(f"**Estimated Value:** ${claim_data.get('estimated_value', 0):,.2f} CAD")
            st.write(f"**Priority:** {claim_data.get('priority', 'N/A')}")
        
        # AI Analysis Summary (if available)
        if claim_data.get('ai_analysis'):
            st.markdown("---")
            st.markdown("## AI Analysis Summary")
            self._render_ai_summary(claim_data['ai_analysis'])
        
        # Contact information
        st.markdown("---")
        st.markdown("## Need Help?")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("""
            ### Contact Claims Team
            **Phone:** 1-800-ZURICH-1  
            **Email:** <EMAIL>  
            **Hours:** Mon-Fri 8AM-8PM EST
            """)
        
        with col2:
            st.markdown("""
            ### Emergency Contact
            **24/7 Hotline:** 1-800-EMERGENCY  
            **For urgent matters only**
            """)
        
        with col3:
            st.markdown("""
            ### Online Support
            **Live Chat:** Available on website  
            **FAQ:** [Visit Help Center](https://help.zurich.ca)
            """)
    
    def _get_claim_data(self, claim_id: str) -> Optional[Dict[str, Any]]:
        """Get claim data from database"""
        try:
            # This would be an async call in production
            # For now, return mock data
            return {
                'claim_id': claim_id,
                'customer_name': 'John Smith',
                'status': 'under_review',
                'created_date': '2025-01-15',
                'claim_type': 'Auto Liability',
                'incident_date': '2025-01-10',
                'location': 'Toronto, ON',
                'policy_number': 'POL-12345',
                'assigned_agent': 'Sarah Johnson',
                'documents_count': 3,
                'estimated_value': 15000.00,
                'priority': 'Medium',
                'ai_analysis': {
                    'coverage': 'Covered',
                    'fraud_risk': 'Low (15/100)',
                    'liability': '60% insured fault',
                    'recommendation': 'Approve with conditions'
                }
            }
        except Exception as e:
            st.error(f"Error retrieving claim data: {e}")
            return None
    
    def _get_timeline_data(self, claim_data: Dict[str, Any]) -> list:
        """Get timeline data for the claim"""
        timeline = [
            {
                'date': claim_data.get('created_date', 'N/A'),
                'time': '2:30 PM',
                'status': 'Claim Received',
                'description': 'Email received and processed',
                'icon': '✅',
                'completed': True
            },
            {
                'date': claim_data.get('created_date', 'N/A'),
                'time': '2:32 PM',
                'status': 'Document Processing',
                'description': f"{claim_data.get('documents_count', 0)} documents processed via OCR",
                'icon': '✅',
                'completed': True
            },
            {
                'date': claim_data.get('created_date', 'N/A'),
                'time': '2:35 PM',
                'status': 'AI Analysis Complete',
                'description': 'Coverage analysis and liability assessment completed',
                'icon': '✅',
                'completed': True
            },
            {
                'date': claim_data.get('created_date', 'N/A'),
                'time': '2:40 PM',
                'status': 'Agent Assigned',
                'description': f"{claim_data.get('assigned_agent', 'Agent')} assigned to your case",
                'icon': '✅',
                'completed': True
            },
            {
                'date': 'Current',
                'time': 'In Progress',
                'status': 'Under Review',
                'description': 'Agent reviewing claim details and documents',
                'icon': '🔄',
                'completed': False
            },
            {
                'date': 'Pending',
                'time': 'Expected: 4 hours',
                'status': 'Decision',
                'description': 'Final decision and settlement offer',
                'icon': '⏳',
                'completed': False
            }
        ]
        
        return timeline
    
    def _render_timeline(self, timeline_data: list):
        """Render the timeline component"""
        for i, item in enumerate(timeline_data):
            col1, col2, col3 = st.columns([0.1, 0.2, 0.7])
            
            with col1:
                if item['completed']:
                    st.markdown(f"<div style='text-align: center; color: #28a745; font-size: 1.5rem;'>{item['icon']}</div>", unsafe_allow_html=True)
                else:
                    st.markdown(f"<div style='text-align: center; color: #ffc107; font-size: 1.5rem;'>{item['icon']}</div>", unsafe_allow_html=True)
            
            with col2:
                st.markdown(f"""
                <div style='text-align: right;'>
                    <p style='margin: 0; font-weight: bold; color: #1f4e79;'>{item['date']}</p>
                    <p style='margin: 0; font-size: 0.9rem; color: #666;'>{item['time']}</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col3:
                status_color = "#28a745" if item['completed'] else "#ffc107"
                st.markdown(f"""
                <div style='border-left: 3px solid {status_color}; padding-left: 1rem; margin-bottom: 1rem;'>
                    <h4 style='margin: 0; color: {status_color};'>{item['status']}</h4>
                    <p style='margin: 0; color: #666;'>{item['description']}</p>
                </div>
                """, unsafe_allow_html=True)
    
    def _render_ai_summary(self, ai_analysis: Dict[str, Any]):
        """Render AI analysis summary"""
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="Coverage",
                value=ai_analysis.get('coverage', 'N/A')
            )
        
        with col2:
            st.metric(
                label="Fraud Risk",
                value=ai_analysis.get('fraud_risk', 'N/A')
            )
        
        with col3:
            st.metric(
                label="Liability",
                value=ai_analysis.get('liability', 'N/A')
            )
        
        with col4:
            st.metric(
                label="Recommendation",
                value=ai_analysis.get('recommendation', 'N/A')
            )

def main():
    """Main function to run the tracking page"""
    st.title("Claim Tracking")
    
    # Get claim ID from URL parameters or user input
    claim_id = st.text_input("Enter your Claim ID:", placeholder="e.g., CLAIM-2025-ABC12345")
    
    if claim_id:
        # Initialize tracking page
        config = {
            'tracking_url': 'https://claims.zurich.ca/track',
            'review_url': 'https://claims.zurich.ca/review'
        }
        
        tracking_page = ClaimTrackingPage(None, config)
        tracking_page.render_tracking_page(claim_id)

if __name__ == "__main__":
    main() 