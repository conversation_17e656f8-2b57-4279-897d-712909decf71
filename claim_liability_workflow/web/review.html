<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claim Review - Zurich Insurance</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/review-styles.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img src="https://www.zurich.com/-/media/project/zwp/zurich/dotcom/global/images/logos/zurich-logo-white.svg" alt="Zurich Insurance" height="40">
                </div>
                <div class="header-actions">
                    <button id="saveDraftBtn" class="btn-secondary">
                        <i class="fas fa-save"></i>
                        Save Draft
                    </button>
                    <button id="closeBtn" class="btn-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Claim Header -->
            <div class="claim-header">
                <div class="claim-info">
                    <h1 id="claimId" class="claim-id">CLAIM-2025-ABC12345</h1>
                    <div class="claim-meta">
                        <span class="customer-info">Customer: <strong id="customerName">John Smith</strong></span>
                        <span class="policy-info">Policy: <strong id="policyNumber">POL-12345</strong></span>
                        <span class="status-info">Status: <strong id="claimStatus">Under Review</strong></span>
                    </div>
                </div>
                <div class="claim-actions">
                    <button id="approveBtn" class="btn-primary">
                        <i class="fas fa-check"></i>
                        Approve Claim
                    </button>
                    <button id="denyBtn" class="btn-danger">
                        <i class="fas fa-times"></i>
                        Deny Claim
                    </button>
                    <button id="requestInfoBtn" class="btn-warning">
                        <i class="fas fa-question"></i>
                        Request Info
                    </button>
                    <button id="escalateBtn" class="btn-secondary">
                        <i class="fas fa-arrow-up"></i>
                        Escalate
                    </button>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="content-grid">
                <!-- Left Column - AI Analysis -->
                <div class="left-column">
                    <!-- AI Analysis Summary -->
                    <div class="analysis-section">
                        <h2 class="section-title">
                            <i class="fas fa-robot"></i>
                            AI Analysis Summary
                        </h2>
                        <div class="analysis-grid">
                            <div class="analysis-card">
                                <div class="analysis-header">
                                    <i class="fas fa-shield-alt"></i>
                                    <h3>Coverage Assessment</h3>
                                </div>
                                <div class="analysis-content">
                                    <div class="analysis-item">
                                        <span class="label">Policy Valid:</span>
                                        <span id="policyValid" class="value success">Yes</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="label">Coverage:</span>
                                        <span id="coverageStatus" class="value success">Covered</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="label">Policy Period:</span>
                                        <span id="policyPeriod" class="value">Jan 1 - Dec 31, 2025</span>
                                    </div>
                                </div>
                            </div>

                            <div class="analysis-card">
                                <div class="analysis-header">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <h3>Fraud Analysis</h3>
                                </div>
                                <div class="analysis-content">
                                    <div class="analysis-item">
                                        <span class="label">Risk Score:</span>
                                        <span id="fraudRisk" class="value low-risk">15/100</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="label">Recommendation:</span>
                                        <span id="fraudRecommendation" class="value success">Approve</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="label">Red Flags:</span>
                                        <span id="redFlags" class="value">None detected</span>
                                    </div>
                                </div>
                            </div>

                            <div class="analysis-card">
                                <div class="analysis-header">
                                    <i class="fas fa-balance-scale"></i>
                                    <h3>Liability Assessment</h3>
                                </div>
                                <div class="analysis-content">
                                    <div class="analysis-item">
                                        <span class="label">Fault Determination:</span>
                                        <span id="faultDetermination" class="value">Clear Liability</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="label">Fault Percentage:</span>
                                        <span id="faultPercentage" class="value">60% insured</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="label">Evidence Strength:</span>
                                        <span id="evidenceStrength" class="value success">Strong</span>
                                    </div>
                                </div>
                            </div>

                            <div class="analysis-card">
                                <div class="analysis-header">
                                    <i class="fas fa-dollar-sign"></i>
                                    <h3>Loss Quantum</h3>
                                </div>
                                <div class="analysis-content">
                                    <div class="analysis-item">
                                        <span class="label">Total Value:</span>
                                        <span id="totalValue" class="value">$15,000 CAD</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="label">Property Damage:</span>
                                        <span id="propertyDamage" class="value">$8,000 CAD</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="label">Bodily Injury:</span>
                                        <span id="bodilyInjury" class="value">$7,000 CAD</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Review -->
                    <div class="documents-section">
                        <h2 class="section-title">
                            <i class="fas fa-file-alt"></i>
                            Documents Review
                        </h2>
                        <div class="documents-grid" id="documentsGrid">
                            <!-- Documents will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Right Column - Decision Panel -->
                <div class="right-column">
                    <!-- Decision Panel -->
                    <div class="decision-panel">
                        <h2 class="section-title">
                            <i class="fas fa-gavel"></i>
                            Decision Panel
                        </h2>
                        
                        <!-- Liability Adjustment -->
                        <div class="decision-section">
                            <h3>Liability Split Adjustment</h3>
                            <div class="liability-slider">
                                <label for="liabilitySlider">Insured Fault Percentage: <span id="liabilityValue">60%</span></label>
                                <input type="range" id="liabilitySlider" min="0" max="100" value="60" class="slider">
                                <div class="slider-labels">
                                    <span>0%</span>
                                    <span>100%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Settlement Amount -->
                        <div class="decision-section">
                            <h3>Settlement Amount</h3>
                            <div class="amount-input">
                                <label for="settlementAmount">Amount (CAD):</label>
                                <input type="number" id="settlementAmount" value="9000" min="0" step="100" class="amount-field">
                                <span class="currency">CAD</span>
                            </div>
                            <p class="amount-note">Based on 60% liability of $15,000 total value</p>
                        </div>

                        <!-- Decision Reasoning -->
                        <div class="decision-section">
                            <h3>Decision Reasoning</h3>
                            <textarea id="decisionReasoning" class="reasoning-textarea" placeholder="Provide detailed reasoning for your decision..."></textarea>
                        </div>

                        <!-- Manager Approval -->
                        <div class="decision-section">
                            <h3>Manager Approval</h3>
                            <div class="manager-input">
                                <label for="managerEmail">Manager Email:</label>
                                <input type="email" id="managerEmail" placeholder="<EMAIL>" class="email-field">
                            </div>
                            <p class="approval-note">All decisions require manager approval before finalization</p>
                        </div>

                        <!-- Submit Decision -->
                        <div class="decision-section">
                            <button id="submitDecisionBtn" class="btn-primary btn-large">
                                <i class="fas fa-paper-plane"></i>
                                Submit for Manager Review
                            </button>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <h3>Quick Actions</h3>
                        <div class="action-buttons">
                            <button class="action-btn" data-action="request-documents">
                                <i class="fas fa-file-upload"></i>
                                Request Documents
                            </button>
                            <button class="action-btn" data-action="schedule-call">
                                <i class="fas fa-phone"></i>
                                Schedule Call
                            </button>
                            <button class="action-btn" data-action="add-note">
                                <i class="fas fa-sticky-note"></i>
                                Add Note
                            </button>
                            <button class="action-btn" data-action="view-history">
                                <i class="fas fa-history"></i>
                                View History
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modals -->
    <div id="confirmationModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Confirm Decision</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p id="modalMessage">Are you sure you want to submit this decision for manager review?</p>
            </div>
            <div class="modal-footer">
                <button id="modalCancel" class="btn-secondary">Cancel</button>
                <button id="modalConfirm" class="btn-primary">Confirm</button>
            </div>
        </div>
    </div>

    <script src="/static/review-script.js"></script>
</body>
</html> 