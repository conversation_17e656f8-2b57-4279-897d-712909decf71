<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manager Approval - Zurich Insurance</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="approve-styles.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img src="https://www.zurich.com/-/media/project/zwp/zurich/dotcom/global/images/logos/zurich-logo-white.svg" alt="Zurich Insurance" height="40">
                </div>
                <div class="header-actions">
                    <span class="user-info">
                        <i class="fas fa-user-circle"></i>
                        <span id="managerName">Manager Name</span>
                    </span>
                    <button id="closeBtn" class="btn-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Approval Header -->
            <div class="approval-header">
                <div class="approval-info">
                    <h1 class="approval-title">Claim Review Required</h1>
                    <p class="approval-subtitle">Agent has submitted a decision requiring your approval</p>
                </div>
                <div class="approval-urgency">
                    <div class="urgency-badge high">
                        <i class="fas fa-clock"></i>
                        <span>24 Hour Deadline</span>
                    </div>
                </div>
            </div>

            <!-- Claim Summary -->
            <div class="claim-summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <label>Claim ID</label>
                        <span id="claimId" class="value">CLAIM-2025-ABC12345</span>
                    </div>
                    <div class="summary-item">
                        <label>Customer</label>
                        <span id="customerName" class="value">John Smith</span>
                    </div>
                    <div class="summary-item">
                        <label>Claim Type</label>
                        <span id="claimType" class="value">Auto Liability</span>
                    </div>
                    <div class="summary-item">
                        <label>Agent</label>
                        <span id="agentName" class="value">Sarah Johnson</span>
                    </div>
                    <div class="summary-item">
                        <label>Submitted</label>
                        <span id="submittedTime" class="value">2 hours ago</span>
                    </div>
                    <div class="summary-item">
                        <label>Priority</label>
                        <span id="priority" class="value high-priority">High</span>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="content-grid">
                <!-- Left Column - Agent Decision -->
                <div class="left-column">
                    <!-- Agent Decision -->
                    <div class="decision-section">
                        <h2 class="section-title">
                            <i class="fas fa-user-tie"></i>
                            Agent's Decision
                        </h2>
                        <div class="decision-card">
                            <div class="decision-header">
                                <div class="decision-badge approve">
                                    <i class="fas fa-check"></i>
                                    <span id="agentDecision">Approve</span>
                                </div>
                                <div class="decision-amount">
                                    <span class="amount-label">Settlement Amount</span>
                                    <span id="settlementAmount" class="amount-value">$9,000 CAD</span>
                                </div>
                            </div>
                            <div class="decision-details">
                                <div class="detail-item">
                                    <label>Liability Split:</label>
                                    <span id="liabilitySplit">60% insured fault</span>
                                </div>
                                <div class="detail-item">
                                    <label>Processing Time:</label>
                                    <span id="processingTime">45 minutes</span>
                                </div>
                                <div class="detail-item">
                                    <label>Confidence Level:</label>
                                    <span id="confidenceLevel">High (85%)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Agent Reasoning -->
                    <div class="reasoning-section">
                        <h2 class="section-title">
                            <i class="fas fa-comments"></i>
                            Agent's Reasoning
                        </h2>
                        <div class="reasoning-card">
                            <div id="agentReasoning" class="reasoning-text">
                                Based on the police report and witness statements, the insured driver was clearly at fault for the collision. The damage assessment shows $8,000 in property damage and $7,000 in medical expenses. Given the clear liability and supporting documentation, I recommend approving the claim with a 60% liability split, resulting in a $9,000 settlement.
                            </div>
                        </div>
                    </div>

                    <!-- AI Analysis Summary -->
                    <div class="ai-analysis-section">
                        <h2 class="section-title">
                            <i class="fas fa-robot"></i>
                            AI Analysis Summary
                        </h2>
                        <div class="analysis-grid">
                            <div class="analysis-card">
                                <div class="analysis-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="analysis-content">
                                    <h4>Coverage</h4>
                                    <p id="coverageStatus" class="success">Covered</p>
                                </div>
                            </div>
                            <div class="analysis-card">
                                <div class="analysis-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="analysis-content">
                                    <h4>Fraud Risk</h4>
                                    <p id="fraudRisk" class="low-risk">Low (15/100)</p>
                                </div>
                            </div>
                            <div class="analysis-card">
                                <div class="analysis-icon">
                                    <i class="fas fa-balance-scale"></i>
                                </div>
                                <div class="analysis-content">
                                    <h4>Liability</h4>
                                    <p id="liabilityAssessment">Clear (60% insured)</p>
                                </div>
                            </div>
                            <div class="analysis-card">
                                <div class="analysis-icon">
                                    <i class="fas fa-lightbulb"></i>
                                </div>
                                <div class="analysis-content">
                                    <h4>Recommendation</h4>
                                    <p id="aiRecommendation">Approve with conditions</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Review -->
                    <div class="documents-section">
                        <h2 class="section-title">
                            <i class="fas fa-file-alt"></i>
                            Supporting Documents
                        </h2>
                        <div class="documents-list" id="documentsList">
                            <!-- Documents will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Right Column - Manager Decision -->
                <div class="right-column">
                    <!-- Manager Decision Panel -->
                    <div class="manager-decision-panel">
                        <h2 class="section-title">
                            <i class="fas fa-gavel"></i>
                            Your Decision
                        </h2>
                        
                        <!-- Decision Options -->
                        <div class="decision-options">
                            <button id="approveBtn" class="decision-btn approve active">
                                <i class="fas fa-check"></i>
                                <span>Approve</span>
                            </button>
                            <button id="modifyBtn" class="decision-btn modify">
                                <i class="fas fa-edit"></i>
                                <span>Modify</span>
                            </button>
                            <button id="rejectBtn" class="decision-btn reject">
                                <i class="fas fa-times"></i>
                                <span>Reject</span>
                            </button>
                        </div>

                        <!-- Modification Options (Hidden by default) -->
                        <div id="modificationPanel" class="modification-panel hidden">
                            <h3>Modify Decision</h3>
                            
                            <!-- Liability Adjustment -->
                            <div class="modification-section">
                                <label for="newLiabilitySlider">New Liability Split: <span id="newLiabilityValue">60%</span></label>
                                <input type="range" id="newLiabilitySlider" min="0" max="100" value="60" class="slider">
                                <div class="slider-labels">
                                    <span>0%</span>
                                    <span>100%</span>
                                </div>
                            </div>

                            <!-- Amount Adjustment -->
                            <div class="modification-section">
                                <label for="newAmount">New Settlement Amount (CAD):</label>
                                <input type="number" id="newAmount" value="9000" min="0" step="100" class="amount-field">
                            </div>

                            <!-- Reason for Modification -->
                            <div class="modification-section">
                                <label for="modificationReason">Reason for Modification:</label>
                                <textarea id="modificationReason" class="reasoning-textarea" placeholder="Explain why you're modifying the decision..."></textarea>
                            </div>
                        </div>

                        <!-- Rejection Options (Hidden by default) -->
                        <div id="rejectionPanel" class="rejection-panel hidden">
                            <h3>Reject Decision</h3>
                            
                            <!-- Rejection Reason -->
                            <div class="rejection-section">
                                <label for="rejectionReason">Reason for Rejection:</label>
                                <textarea id="rejectionReason" class="reasoning-textarea" placeholder="Explain why you're rejecting this decision..."></textarea>
                            </div>

                            <!-- Next Steps -->
                            <div class="rejection-section">
                                <label for="nextSteps">Recommended Next Steps:</label>
                                <select id="nextSteps" class="select-field">
                                    <option value="request_more_info">Request More Information</option>
                                    <option value="reassign_agent">Reassign to Different Agent</option>
                                    <option value="escalate_specialist">Escalate to Specialist</option>
                                    <option value="legal_review">Send for Legal Review</option>
                                </select>
                            </div>
                        </div>

                        <!-- Manager Comments -->
                        <div class="comments-section">
                            <h3>Your Comments</h3>
                            <textarea id="managerComments" class="reasoning-textarea" placeholder="Add any additional comments or instructions for the agent..."></textarea>
                        </div>

                        <!-- Submit Decision -->
                        <div class="submit-section">
                            <button id="submitDecisionBtn" class="btn-primary btn-large">
                                <i class="fas fa-paper-plane"></i>
                                Submit Decision
                            </button>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <h3>Quick Actions</h3>
                        <div class="action-buttons">
                            <button class="action-btn" data-action="view-full-claim">
                                <i class="fas fa-eye"></i>
                                View Full Claim
                            </button>
                            <button class="action-btn" data-action="contact-agent">
                                <i class="fas fa-phone"></i>
                                Contact Agent
                            </button>
                            <button class="action-btn" data-action="request-info">
                                <i class="fas fa-question"></i>
                                Request Info
                            </button>
                            <button class="action-btn" data-action="escalate">
                                <i class="fas fa-arrow-up"></i>
                                Escalate
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Confirmation Modal -->
    <div id="confirmationModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Confirm Decision</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p id="modalMessage">Are you sure you want to submit this decision?</p>
                <div id="decisionSummary" class="decision-summary">
                    <!-- Decision summary will be populated -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="modalCancel" class="btn-secondary">Cancel</button>
                <button id="modalConfirm" class="btn-primary">Confirm</button>
            </div>
        </div>
    </div>

    <script src="approve-script.js"></script>
</body>
</html> 