// Claim Review Application
class ClaimReview {
    constructor() {
        this.claimData = null;
        this.currentDecision = {
            liability: 60,
            settlementAmount: 9000,
            reasoning: '',
            managerEmail: ''
        };
        this.initializeEventListeners();
        this.loadClaimData();
    }

    initializeEventListeners() {
        // Liability slider
        const liabilitySlider = document.getElementById('liabilitySlider');
        if (liabilitySlider) {
            liabilitySlider.addEventListener('input', (e) => this.updateLiability(e.target.value));
        }

        // Settlement amount
        const settlementAmount = document.getElementById('settlementAmount');
        if (settlementAmount) {
            settlementAmount.addEventListener('input', (e) => this.updateSettlementAmount(e.target.value));
        }

        // Decision reasoning
        const decisionReasoning = document.getElementById('decisionReasoning');
        if (decisionReasoning) {
            decisionReasoning.addEventListener('input', (e) => this.updateReasoning(e.target.value));
        }

        // Manager email
        const managerEmail = document.getElementById('managerEmail');
        if (managerEmail) {
            managerEmail.addEventListener('input', (e) => this.updateManagerEmail(e.target.value));
        }

        // Action buttons
        document.getElementById('approveBtn')?.addEventListener('click', () => this.setDecisionType('approve'));
        document.getElementById('denyBtn')?.addEventListener('click', () => this.setDecisionType('deny'));
        document.getElementById('requestInfoBtn')?.addEventListener('click', () => this.setDecisionType('request_info'));
        document.getElementById('escalateBtn')?.addEventListener('click', () => this.escalateClaim());

        // Submit decision
        document.getElementById('submitDecisionBtn')?.addEventListener('click', () => this.submitDecision());

        // Save draft
        document.getElementById('saveDraftBtn')?.addEventListener('click', () => this.saveDraft());

        // Close button
        document.getElementById('closeBtn')?.addEventListener('click', () => this.closeReview());

        // Modal events
        document.getElementById('modalCancel')?.addEventListener('click', () => this.hideModal());
        document.getElementById('modalConfirm')?.addEventListener('click', () => this.confirmDecision());
        document.querySelector('.modal-close')?.addEventListener('click', () => this.hideModal());

        // Quick action buttons
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleQuickAction(e.target.closest('.action-btn').dataset.action));
        });

        // Auto-save every 30 seconds
        setInterval(() => this.autoSave(), 30000);
    }

    async loadClaimData() {
        // Get claim ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const claimId = urlParams.get('claim');
        
        if (!claimId) {
            this.showError('No claim ID provided');
            return;
        }

        try {
            // In production, this would be an actual API call
            this.claimData = await this.fetchClaimData(claimId);
            this.displayClaimData();
            this.loadDocuments();
        } catch (error) {
            console.error('Error loading claim data:', error);
            this.showError('Failed to load claim data');
        }
    }

    async fetchClaimData(claimId) {
        // Simulate API call with mock data
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    claim_id: claimId,
                    customer_name: 'John Smith',
                    policy_number: 'POL-12345',
                    status: 'under_review',
                    claim_type: 'Auto Liability',
                    incident_date: '2025-01-10',
                    location: 'Toronto, ON',
                    total_value: 15000,
                    property_damage: 8000,
                    bodily_injury: 7000,
                    ai_analysis: {
                        policy_valid: true,
                        coverage: 'Covered',
                        fraud_risk: 15,
                        fraud_recommendation: 'Approve',
                        red_flags: 'None detected',
                        fault_determination: 'Clear Liability',
                        fault_percentage: 60,
                        evidence_strength: 'Strong'
                    }
                });
            }, 1000);
        });
    }

    displayClaimData() {
        if (!this.claimData) return;

        // Update claim header
        document.getElementById('claimId').textContent = this.claimData.claim_id;
        document.getElementById('customerName').textContent = this.claimData.customer_name;
        document.getElementById('policyNumber').textContent = this.claimData.policy_number;
        document.getElementById('claimStatus').textContent = this.formatStatus(this.claimData.status);

        // Update AI analysis
        const analysis = this.claimData.ai_analysis;
        document.getElementById('policyValid').textContent = analysis.policy_valid ? 'Yes' : 'No';
        document.getElementById('coverageStatus').textContent = analysis.coverage;
        document.getElementById('fraudRisk').textContent = `${analysis.fraud_risk}/100`;
        document.getElementById('fraudRecommendation').textContent = analysis.fraud_recommendation;
        document.getElementById('redFlags').textContent = analysis.red_flags;
        document.getElementById('faultDetermination').textContent = analysis.fault_determination;
        document.getElementById('faultPercentage').textContent = `${analysis.fault_percentage}% insured`;
        document.getElementById('evidenceStrength').textContent = analysis.evidence_strength;

        // Update loss quantum
        document.getElementById('totalValue').textContent = `$${this.claimData.total_value.toLocaleString('en-CA')} CAD`;
        document.getElementById('propertyDamage').textContent = `$${this.claimData.property_damage.toLocaleString('en-CA')} CAD`;
        document.getElementById('bodilyInjury').textContent = `$${this.claimData.bodily_injury.toLocaleString('en-CA')} CAD`;

        // Set initial settlement amount
        const initialAmount = Math.round(this.claimData.total_value * (analysis.fault_percentage / 100));
        document.getElementById('settlementAmount').value = initialAmount;
        this.currentDecision.settlementAmount = initialAmount;
    }

    loadDocuments() {
        const documentsGrid = document.getElementById('documentsGrid');
        const mockDocuments = [
            {
                title: 'Police Report',
                type: 'pdf',
                size: '2.3 MB',
                uploaded: '2025-01-15 14:30'
            },
            {
                title: 'Medical Records',
                type: 'pdf',
                size: '1.8 MB',
                uploaded: '2025-01-15 14:32'
            },
            {
                title: 'Witness Statement',
                type: 'docx',
                size: '0.5 MB',
                uploaded: '2025-01-15 14:35'
            }
        ];

        documentsGrid.innerHTML = '';
        mockDocuments.forEach(doc => {
            const docCard = document.createElement('div');
            docCard.className = 'document-card';
            docCard.innerHTML = `
                <div class="document-header">
                    <div class="document-icon">
                        <i class="fas fa-${this.getDocumentIcon(doc.type)}"></i>
                    </div>
                    <div>
                        <div class="document-title">${doc.title}</div>
                        <div class="document-meta">${doc.size} • ${doc.uploaded}</div>
                    </div>
                </div>
            `;
            docCard.addEventListener('click', () => this.viewDocument(doc));
            documentsGrid.appendChild(docCard);
        });
    }

    getDocumentIcon(type) {
        switch (type) {
            case 'pdf': return 'file-pdf';
            case 'docx': return 'file-word';
            case 'xlsx': return 'file-excel';
            case 'jpg':
            case 'png': return 'file-image';
            default: return 'file';
        }
    }

    updateLiability(value) {
        this.currentDecision.liability = parseInt(value);
        document.getElementById('liabilityValue').textContent = `${value}%`;
        
        // Update settlement amount based on liability
        const newAmount = Math.round(this.claimData.total_value * (value / 100));
        document.getElementById('settlementAmount').value = newAmount;
        this.currentDecision.settlementAmount = newAmount;
        
        // Update note
        document.querySelector('.amount-note').textContent = `Based on ${value}% liability of $${this.claimData.total_value.toLocaleString('en-CA')} total value`;
    }

    updateSettlementAmount(value) {
        this.currentDecision.settlementAmount = parseFloat(value) || 0;
    }

    updateReasoning(value) {
        this.currentDecision.reasoning = value;
    }

    updateManagerEmail(value) {
        this.currentDecision.managerEmail = value;
    }

    setDecisionType(type) {
        this.currentDecision.type = type;
        
        // Update button states
        document.querySelectorAll('.claim-actions button').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeBtn = document.querySelector(`#${type}Btn`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Update reasoning placeholder based on decision type
        const reasoningTextarea = document.getElementById('decisionReasoning');
        switch (type) {
            case 'approve':
                reasoningTextarea.placeholder = 'Provide reasoning for approval...';
                break;
            case 'deny':
                reasoningTextarea.placeholder = 'Provide reasoning for denial...';
                break;
            case 'request_info':
                reasoningTextarea.placeholder = 'Specify what additional information is needed...';
                break;
        }
    }

    async submitDecision() {
        if (!this.validateDecision()) {
            return;
        }

        this.showConfirmationModal();
    }

    validateDecision() {
        if (!this.currentDecision.type) {
            this.showError('Please select a decision type (Approve, Deny, or Request Info)');
            return false;
        }

        if (!this.currentDecision.reasoning.trim()) {
            this.showError('Please provide reasoning for your decision');
            return false;
        }

        if (!this.currentDecision.managerEmail.trim()) {
            this.showError('Please enter manager email for approval');
            return false;
        }

        if (!this.isValidEmail(this.currentDecision.managerEmail)) {
            this.showError('Please enter a valid manager email address');
            return false;
        }

        return true;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showConfirmationModal() {
        const modal = document.getElementById('confirmationModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');

        modalTitle.textContent = 'Confirm Decision Submission';
        modalMessage.textContent = `Are you sure you want to submit this ${this.currentDecision.type} decision for manager review? This action cannot be undone.`;

        modal.classList.remove('hidden');
    }

    hideModal() {
        document.getElementById('confirmationModal').classList.add('hidden');
    }

    async confirmDecision() {
        this.hideModal();
        
        try {
            // Show loading state
            const submitBtn = document.getElementById('submitDecisionBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
            submitBtn.disabled = true;

            // Simulate API call
            await this.submitDecisionToAPI();

            // Show success message
            this.showSuccess('Decision submitted successfully! Manager will be notified for approval.');

            // Reset form
            this.resetForm();

        } catch (error) {
            console.error('Error submitting decision:', error);
            this.showError('Failed to submit decision. Please try again.');
        } finally {
            // Restore button
            const submitBtn = document.getElementById('submitDecisionBtn');
            submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Submit for Manager Review';
            submitBtn.disabled = false;
        }
    }

    async submitDecisionToAPI() {
        // Simulate API call
        return new Promise((resolve) => {
            setTimeout(() => {
                console.log('Decision submitted:', this.currentDecision);
                resolve();
            }, 2000);
        });
    }

    async saveDraft() {
        try {
            // Save to localStorage for demo purposes
            localStorage.setItem('claimDraft', JSON.stringify({
                claimId: this.claimData.claim_id,
                decision: this.currentDecision,
                timestamp: new Date().toISOString()
            }));

            this.showSuccess('Draft saved successfully!');
        } catch (error) {
            console.error('Error saving draft:', error);
            this.showError('Failed to save draft');
        }
    }

    autoSave() {
        if (this.currentDecision.reasoning.trim()) {
            this.saveDraft();
        }
    }

    escalateClaim() {
        this.showConfirmationModal('escalate');
    }

    handleQuickAction(action) {
        switch (action) {
            case 'request-documents':
                this.requestDocuments();
                break;
            case 'schedule-call':
                this.scheduleCall();
                break;
            case 'add-note':
                this.addNote();
                break;
            case 'view-history':
                this.viewHistory();
                break;
        }
    }

    requestDocuments() {
        this.showModal('Request Documents', 'What additional documents do you need?');
    }

    scheduleCall() {
        this.showModal('Schedule Call', 'When would you like to schedule a call with the customer?');
    }

    addNote() {
        this.showModal('Add Note', 'Enter your note:');
    }

    viewHistory() {
        this.showModal('Claim History', 'Viewing claim history...');
    }

    showModal(title, message) {
        const modal = document.getElementById('confirmationModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');

        modalTitle.textContent = title;
        modalMessage.textContent = message;

        modal.classList.remove('hidden');
    }

    viewDocument(doc) {
        // In production, this would open the document viewer
        console.log('Viewing document:', doc);
        this.showModal('Document Viewer', `Opening ${doc.title}...`);
    }

    closeReview() {
        if (this.currentDecision.reasoning.trim()) {
            if (confirm('You have unsaved changes. Are you sure you want to close?')) {
                window.close();
            }
        } else {
            window.close();
        }
    }

    resetForm() {
        this.currentDecision = {
            liability: 60,
            settlementAmount: 9000,
            reasoning: '',
            managerEmail: '',
            type: null
        };

        document.getElementById('liabilitySlider').value = 60;
        document.getElementById('liabilityValue').textContent = '60%';
        document.getElementById('settlementAmount').value = 9000;
        document.getElementById('decisionReasoning').value = '';
        document.getElementById('managerEmail').value = '';

        // Reset button states
        document.querySelectorAll('.claim-actions button').forEach(btn => {
            btn.classList.remove('active');
        });
    }

    formatStatus(status) {
        return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    showSuccess(message) {
        // Create success notification
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;
        this.showNotification(notification);
    }

    showError(message) {
        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'notification error';
        notification.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        `;
        this.showNotification(notification);
    }

    showNotification(notification) {
        // Add notification styles if not already present
        if (!document.getElementById('notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'notification-styles';
            styles.textContent = `
                .notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 1rem 1.5rem;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    z-index: 1001;
                    animation: slideIn 0.3s ease;
                }
                .notification.success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                }
                .notification.error {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                }
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }

        document.body.appendChild(notification);

        // Remove notification after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ClaimReview();
});

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + S to save draft
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        document.getElementById('saveDraftBtn').click();
    }
    
    // Ctrl/Cmd + Enter to submit
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        document.getElementById('submitDecisionBtn').click();
    }
    
    // Escape to close modal
    if (e.key === 'Escape') {
        const modal = document.getElementById('confirmationModal');
        if (!modal.classList.contains('hidden')) {
            modal.classList.add('hidden');
        }
    }
});

// Add active button styles
const activeButtonStyles = `
    .claim-actions button.active {
        transform: scale(1.05);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .btn-primary.active {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
    }
    
    .btn-danger.active {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }
    
    .btn-warning.active {
        background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = activeButtonStyles;
document.head.appendChild(styleSheet); 