// Claim Tracking Application
class ClaimTracker {
    constructor() {
        this.apiBaseUrl = 'https://api.claims.zurich.ca'; // Replace with actual API URL
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Track claim button
        const trackBtn = document.getElementById('trackClaimBtn');
        if (trackBtn) {
            trackBtn.addEventListener('click', () => this.trackClaim());
        }

        // Enter key on input
        const claimInput = document.getElementById('claimIdInput');
        if (claimInput) {
            claimInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.trackClaim();
                }
            });
        }

        // Try again button
        const tryAgainBtn = document.getElementById('tryAgainBtn');
        if (tryAgainBtn) {
            tryAgainBtn.addEventListener('click', () => this.resetForm());
        }

        // Check for claim ID in URL parameters
        this.checkUrlParameters();
    }

    checkUrlParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        const claimId = urlParams.get('claim');
        if (claimId) {
            document.getElementById('claimIdInput').value = claimId;
            this.trackClaim();
        }
    }

    async trackClaim() {
        const claimId = document.getElementById('claimIdInput').value.trim();
        
        if (!claimId) {
            this.showError('Please enter a claim ID');
            return;
        }

        this.showLoading();
        
        try {
            // In production, this would be an actual API call
            const claimData = await this.fetchClaimData(claimId);
            
            if (claimData) {
                this.displayClaimDetails(claimData);
            } else {
                this.showError('Claim not found. Please check your claim ID.');
            }
        } catch (error) {
            console.error('Error tracking claim:', error);
            this.showError('An error occurred while tracking your claim. Please try again.');
        }
    }

    async fetchClaimData(claimId) {
        // Simulate API call with mock data
        // In production, replace with actual API endpoint
        return new Promise((resolve) => {
            setTimeout(() => {
                // Mock data - replace with actual API call
                const mockData = this.getMockClaimData(claimId);
                resolve(mockData);
            }, 1500);
        });
    }

    getMockClaimData(claimId) {
        // Return null for invalid claim IDs
        if (!claimId.startsWith('CLAIM-')) {
            return null;
        }

        return {
            claim_id: claimId,
            customer_name: 'John Smith',
            status: 'under_review',
            created_date: '2025-01-15',
            claim_type: 'Auto Liability',
            incident_date: '2025-01-10',
            location: 'Toronto, ON',
            policy_number: 'POL-12345',
            assigned_agent: 'Sarah Johnson',
            documents_count: 3,
            estimated_value: 15000.00,
            priority: 'Medium',
            ai_analysis: {
                coverage: 'Covered',
                fraud_risk: 'Low (15/100)',
                liability: '60% insured fault',
                recommendation: 'Approve with conditions'
            },
            timeline: [
                {
                    date: '2025-01-15',
                    time: '2:30 PM',
                    status: 'Claim Received',
                    description: 'Email received and processed',
                    completed: true
                },
                {
                    date: '2025-01-15',
                    time: '2:32 PM',
                    status: 'Document Processing',
                    description: '3 documents processed via OCR',
                    completed: true
                },
                {
                    date: '2025-01-15',
                    time: '2:35 PM',
                    status: 'AI Analysis Complete',
                    description: 'Coverage analysis and liability assessment completed',
                    completed: true
                },
                {
                    date: '2025-01-15',
                    time: '2:40 PM',
                    status: 'Agent Assigned',
                    description: 'Sarah Johnson assigned to your case',
                    completed: true
                },
                {
                    date: 'Current',
                    time: 'In Progress',
                    status: 'Under Review',
                    description: 'Agent reviewing claim details and documents',
                    completed: false
                },
                {
                    date: 'Pending',
                    time: 'Expected: 4 hours',
                    status: 'Decision',
                    description: 'Final decision and settlement offer',
                    completed: false
                }
            ]
        };
    }

    displayClaimDetails(claimData) {
        this.hideAllStates();
        
        // Update claim header
        document.getElementById('claimId').textContent = claimData.claim_id;
        document.getElementById('customerName').textContent = claimData.customer_name;
        
        // Update status
        const statusText = document.getElementById('statusText');
        const statusBadge = document.getElementById('statusBadge');
        statusText.textContent = this.formatStatus(claimData.status);
        statusBadge.className = `status-badge ${this.getStatusClass(claimData.status)}`;
        
        // Update basic information
        document.getElementById('claimType').textContent = claimData.claim_type;
        document.getElementById('incidentDate').textContent = claimData.incident_date;
        document.getElementById('location').textContent = claimData.location;
        document.getElementById('policyNumber').textContent = claimData.policy_number;
        
        // Update processing information
        document.getElementById('assignedAgent').textContent = claimData.assigned_agent || 'Not assigned yet';
        document.getElementById('documentsCount').textContent = claimData.documents_count;
        document.getElementById('estimatedValue').textContent = `$${claimData.estimated_value.toLocaleString('en-CA')} CAD`;
        document.getElementById('priority').textContent = claimData.priority;
        
        // Update AI analysis if available
        if (claimData.ai_analysis) {
            document.getElementById('coverageStatus').textContent = claimData.ai_analysis.coverage;
            document.getElementById('fraudRisk').textContent = claimData.ai_analysis.fraud_risk;
            document.getElementById('liabilityAssessment').textContent = claimData.ai_analysis.liability;
            document.getElementById('recommendation').textContent = claimData.ai_analysis.recommendation;
            document.getElementById('aiAnalysisSection').classList.remove('hidden');
        }
        
        // Render timeline
        this.renderTimeline(claimData.timeline);
        
        // Show claim details
        document.getElementById('claimDetails').classList.remove('hidden');
        
        // Scroll to claim details
        document.getElementById('claimDetails').scrollIntoView({ behavior: 'smooth' });
    }

    renderTimeline(timelineData) {
        const timelineContainer = document.getElementById('timeline');
        timelineContainer.innerHTML = '';
        
        timelineData.forEach((item, index) => {
            const timelineItem = document.createElement('div');
            timelineItem.className = `timeline-item ${this.getTimelineItemClass(item.completed)}`;
            
            timelineItem.innerHTML = `
                <div class="timeline-content">
                    <div class="timeline-header">
                        <span class="timeline-title">${item.status}</span>
                        <span class="timeline-time">${item.time}</span>
                    </div>
                    <div class="timeline-description">${item.description}</div>
                </div>
            `;
            
            timelineContainer.appendChild(timelineItem);
        });
    }

    getTimelineItemClass(completed) {
        if (completed) {
            return 'completed';
        } else if (completed === false) {
            return 'pending';
        } else {
            return 'future';
        }
    }

    formatStatus(status) {
        return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    getStatusClass(status) {
        switch (status) {
            case 'under_review':
            case 'agent_review_complete':
            case 'manager_review_complete':
                return 'pending';
            case 'finalized':
            case 'approved':
                return '';
            case 'denied':
            case 'rejected':
                return 'rejected';
            default:
                return '';
        }
    }

    showLoading() {
        this.hideAllStates();
        document.getElementById('loadingState').classList.remove('hidden');
    }

    showError(message) {
        this.hideAllStates();
        const errorState = document.getElementById('errorState');
        const errorMessage = errorState.querySelector('p');
        errorMessage.textContent = message;
        errorState.classList.remove('hidden');
    }

    hideAllStates() {
        document.getElementById('claimDetails').classList.add('hidden');
        document.getElementById('loadingState').classList.add('hidden');
        document.getElementById('errorState').classList.add('hidden');
    }

    resetForm() {
        document.getElementById('claimIdInput').value = '';
        this.hideAllStates();
        document.getElementById('claimIdInput').focus();
    }

    // Utility method for API calls (for production use)
    async makeApiCall(endpoint, options = {}) {
        const url = `${this.apiBaseUrl}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const response = await fetch(url, { ...defaultOptions, ...options });
        
        if (!response.ok) {
            throw new Error(`API call failed: ${response.status}`);
        }
        
        return response.json();
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ClaimTracker();
});

// Add smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading animation for buttons
document.querySelectorAll('button').forEach(button => {
    button.addEventListener('click', function() {
        if (!this.classList.contains('loading')) {
            this.classList.add('loading');
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            
            setTimeout(() => {
                this.classList.remove('loading');
                this.innerHTML = originalText;
            }, 2000);
        }
    });
});

// Add responsive navigation toggle for mobile
const navToggle = document.createElement('button');
navToggle.className = 'nav-toggle';
navToggle.innerHTML = '<i class="fas fa-bars"></i>';
navToggle.style.display = 'none';

const nav = document.querySelector('.nav');
if (nav) {
    nav.parentNode.insertBefore(navToggle, nav);
    
    navToggle.addEventListener('click', () => {
        nav.classList.toggle('active');
    });
}

// Handle window resize for mobile navigation
window.addEventListener('resize', () => {
    if (window.innerWidth > 768) {
        nav.classList.remove('active');
    }
});

// Add CSS for mobile navigation
const mobileNavStyles = `
    @media (max-width: 768px) {
        .nav-toggle {
            display: block !important;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .nav {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #1f4e79;
            flex-direction: column;
            padding: 1rem;
            gap: 1rem;
        }
        
        .nav.active {
            display: flex;
        }
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = mobileNavStyles;
document.head.appendChild(styleSheet); 