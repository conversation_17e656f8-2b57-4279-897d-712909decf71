/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8fafc;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #1f4e79 0%, #2980b9 100%);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 40px;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.user-info {
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.btn-close {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

/* Approval Header */
.approval-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.approval-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f4e79;
    margin-bottom: 0.5rem;
}

.approval-subtitle {
    color: #64748b;
    font-size: 1.1rem;
}

.urgency-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
}

.urgency-badge.high {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

/* Claim Summary */
.claim-summary {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.summary-item label {
    font-weight: 500;
    color: #64748b;
    font-size: 0.9rem;
}

.summary-item .value {
    font-weight: 600;
    color: #1f4e79;
    font-size: 1rem;
}

.value.high-priority {
    color: #ef4444;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

/* Left Column */
.left-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Section Titles */
.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: #3b82f6;
}

/* Decision Section */
.decision-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.decision-card {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
}

.decision-header {
    background: #f8fafc;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

.decision-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
}

.decision-badge.approve {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.decision-badge.deny {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.decision-badge.modify {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.decision-amount {
    text-align: right;
}

.amount-label {
    display: block;
    font-size: 0.8rem;
    color: #64748b;
    margin-bottom: 0.25rem;
}

.amount-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f4e79;
}

.decision-details {
    padding: 1.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-weight: 500;
    color: #64748b;
}

.detail-item span {
    font-weight: 600;
    color: #1f4e79;
}

/* Reasoning Section */
.reasoning-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.reasoning-card {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    background: #f8fafc;
}

.reasoning-text {
    color: #64748b;
    line-height: 1.7;
    font-size: 0.95rem;
}

/* AI Analysis Section */
.ai-analysis-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.analysis-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.analysis-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.analysis-card:nth-child(1) .analysis-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.analysis-card:nth-child(2) .analysis-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.analysis-card:nth-child(3) .analysis-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.analysis-card:nth-child(4) .analysis-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.analysis-content h4 {
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.analysis-content p {
    color: #64748b;
    font-size: 0.8rem;
    margin: 0;
}

.analysis-content p.success {
    color: #10b981;
}

.analysis-content p.low-risk {
    color: #10b981;
}

.analysis-content p.medium-risk {
    color: #f59e0b;
}

.analysis-content p.high-risk {
    color: #ef4444;
}

/* Documents Section */
.documents-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.documents-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.document-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #f8fafc;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.document-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.document-icon {
    width: 40px;
    height: 40px;
    background: #e2e8f0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
}

.document-info {
    flex: 1;
}

.document-title {
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 0.25rem;
}

.document-meta {
    color: #64748b;
    font-size: 0.8rem;
}

/* Right Column */
.right-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Manager Decision Panel */
.manager-decision-panel {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.decision-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.decision-btn {
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
}

.decision-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.decision-btn.active {
    border-color: #3b82f6;
    color: #3b82f6;
    background: #eff6ff;
}

.decision-btn.approve.active {
    border-color: #10b981;
    color: #10b981;
    background: #ecfdf5;
}

.decision-btn.modify.active {
    border-color: #f59e0b;
    color: #f59e0b;
    background: #fffbeb;
}

.decision-btn.reject.active {
    border-color: #ef4444;
    color: #ef4444;
    background: #fef2f2;
}

/* Modification Panel */
.modification-panel, .rejection-panel {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.modification-panel h3, .rejection-panel h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1rem;
}

.modification-section, .rejection-section {
    margin-bottom: 1.5rem;
}

.modification-section:last-child, .rejection-section:last-child {
    margin-bottom: 0;
}

.modification-section label, .rejection-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #64748b;
}

/* Slider */
.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
    margin-bottom: 0.5rem;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: none;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    color: #64748b;
    font-size: 0.8rem;
}

/* Form Fields */
.amount-field, .select-field {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.amount-field:focus, .select-field:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.reasoning-textarea {
    width: 100%;
    min-height: 100px;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
    outline: none;
    transition: border-color 0.3s ease;
}

.reasoning-textarea:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Comments Section */
.comments-section {
    margin-bottom: 2rem;
}

.comments-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1rem;
}

/* Submit Section */
.submit-section {
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    width: 100%;
    justify-content: center;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(16, 185, 129, 0.3);
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.quick-actions h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1rem;
}

.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.action-btn {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
}

.action-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.action-btn i {
    font-size: 1.2rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #1f4e79;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #64748b;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.modal-close:hover {
    background: #f1f5f9;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.decision-summary {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-secondary {
    background: #64748b;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background: #475569;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .analysis-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .approval-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .decision-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .modal-footer button {
        width: 100%;
    }
} 