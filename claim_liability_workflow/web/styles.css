/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8fafc;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #1f4e79 0%, #2980b9 100%);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 40px;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.nav-link:hover {
    opacity: 0.8;
}

/* Main Content */
.main {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    margin-bottom: 3rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.claim-input-container {
    max-width: 500px;
    margin: 0 auto;
}

.input-group {
    display: flex;
    gap: 0.5rem;
    background: white;
    border-radius: 50px;
    padding: 0.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.claim-input {
    flex: 1;
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    outline: none;
    border-radius: 25px;
}

.track-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.track-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0,0,0,0.2);
}

/* Claim Details */
.claim-details {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.claim-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.claim-id {
    font-size: 2rem;
    font-weight: 700;
    color: #1f4e79;
    margin-bottom: 0.5rem;
}

.customer-name {
    color: #64748b;
    font-size: 1.1rem;
}

.status-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
}

.status-badge.pending {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.status-badge.rejected {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* Timeline */
.timeline-section {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1.5rem;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e2e8f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -0.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background: #10b981;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #e2e8f0;
}

.timeline-item.pending::before {
    background: #f59e0b;
}

.timeline-item.future::before {
    background: #94a3b8;
}

.timeline-content {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 12px;
    border-left: 4px solid #10b981;
}

.timeline-item.pending .timeline-content {
    border-left-color: #f59e0b;
}

.timeline-item.future .timeline-content {
    border-left-color: #94a3b8;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.timeline-title {
    font-weight: 600;
    color: #1f4e79;
}

.timeline-time {
    color: #64748b;
    font-size: 0.9rem;
}

.timeline-description {
    color: #64748b;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.info-card {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.info-item:last-child {
    border-bottom: none;
}

.label {
    font-weight: 500;
    color: #64748b;
}

.value {
    font-weight: 600;
    color: #1f4e79;
}

/* AI Analysis */
.ai-analysis-section {
    margin-bottom: 3rem;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.analysis-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.analysis-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.analysis-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.analysis-card:nth-child(1) .analysis-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.analysis-card:nth-child(2) .analysis-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.analysis-card:nth-child(3) .analysis-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.analysis-card:nth-child(4) .analysis-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.analysis-content h4 {
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 0.25rem;
}

.analysis-content p {
    color: #64748b;
    font-size: 0.9rem;
}

/* Contact Section */
.contact-section {
    margin-bottom: 2rem;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.contact-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
    text-align: center;
    transition: transform 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-2px);
}

.contact-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.contact-card h4 {
    font-weight: 600;
    color: #1f4e79;
    margin-bottom: 1rem;
}

.contact-card p {
    color: #64748b;
    margin-bottom: 0.5rem;
}

.contact-card a {
    color: #3b82f6;
    text-decoration: none;
}

.contact-card a:hover {
    text-decoration: underline;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 4rem 0;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error State */
.error-state {
    text-align: center;
    padding: 4rem 0;
}

.error-icon {
    font-size: 4rem;
    color: #ef4444;
    margin-bottom: 1rem;
}

.error-state h3 {
    color: #1f4e79;
    margin-bottom: 1rem;
}

.error-state p {
    color: #64748b;
    margin-bottom: 2rem;
}

.btn-secondary {
    background: #64748b;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background: #475569;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #f3f4f6;
}

.footer-section a {
    color: #d1d5db;
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .input-group {
        flex-direction: column;
        border-radius: 12px;
    }
    
    .track-btn {
        border-radius: 8px;
    }
    
    .claim-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .timeline {
        padding-left: 1rem;
    }
    
    .timeline::before {
        left: 0.5rem;
    }
    
    .timeline-item {
        padding-left: 1rem;
    }
    
    .timeline-item::before {
        left: -0.25rem;
    }
    
    .info-grid,
    .analysis-grid,
    .contact-grid {
        grid-template-columns: 1fr;
    }
} 