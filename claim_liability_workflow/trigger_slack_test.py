#!/usr/bin/env python3
"""
Direct Slack message trigger for testing the optimized UI
Sends the improved Block Kit notification directly to Slack
"""

import asyncio
import aiohttp
import json
from datetime import datetime
import os
import sys
from typing import Dict, Any

# Add the src directory to the path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append(os.path.dirname(__file__))

from config import Config

# Load configuration
config = Config()
SLACK_WEBHOOK_URL = config.slack.slack_webhook_url

def create_optimized_slack_message() -> Dict[str, Any]:
    """Create the optimized Slack Block Kit message"""
    
    claim_id = "CLAIM-2025-08FEF3DB"
    timestamp = datetime.now().strftime('%I:%M %p')
    
    return {
        "text": f"New Claim Assignment Required: {claim_id}",
        "blocks": [
            # Header with urgency indicator
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🟡 New Claim Assignment Required"
                }
            },
            # Main claim information
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Claim ID:*\n`{claim_id}`"
                    },
                    {
                        "type": "mrkdwn", 
                        "text": "*Type:*\nBodily Injury"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Urgency:*\n🟡 Medium"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Estimated Value:*\n$10,000.00 CAD"
                    }
                ]
            },
            # Customer information
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Customer:* Dinesh Krishna\n*Email:* <EMAIL>"
                }
            },
            # Divider
            {
                "type": "divider"
            },
            # Analysis overview with context
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": "📅 *Incident:* August 27, 2020"
                    }
                ]
            },
            {
                "type": "context", 
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": "📍 *Location:* No Frills location"
                    }
                ]
            },
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": "📎 *Documents:* 4 files processed"
                    }
                ]
            },
            # Divider
            {
                "type": "divider"
            },
            # Risk assessment section
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Risk Assessment:*"
                },
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Coverage:*\n✅ covered"
                    },
                    {
                        "type": "mrkdwn", 
                        "text": "*Fraud Risk:*\n✅ 25/100"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Liability:*\n🔴 80% insured fault"
                    }
                ]
            },
            # Divider
            {
                "type": "divider"
            },
            # Action buttons
            {
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "🔍 Review Claim",
                            "emoji": True
                        },
                        "style": "primary",
                        "url": f"http://localhost:8080/review/{claim_id}",
                        "action_id": "review_claim"
                    },
                    {
                        "type": "button", 
                        "text": {
                            "type": "plain_text",
                            "text": "👤 Assign to Me",
                            "emoji": True
                        },
                        "style": "danger",
                        "action_id": f"assign_to_me_{claim_id}",
                        "confirm": {
                            "title": {
                                "type": "plain_text",
                                "text": "Confirm Assignment"
                            },
                            "text": {
                                "type": "mrkdwn",
                                "text": f"Are you sure you want to assign claim `{claim_id}` to yourself?"
                            },
                            "confirm": {
                                "type": "plain_text",
                                "text": "Yes, Assign"
                            },
                            "deny": {
                                "type": "plain_text",
                                "text": "Cancel"
                            }
                        }
                    },
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text", 
                            "text": "📋 View Details",
                            "emoji": True
                        },
                        "action_id": f"view_details_{claim_id}"
                    }
                ]
            },
            # Footer context
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"⏰ Received at {timestamp} | 🏢 Zurich Insurance Claims"
                    }
                ]
            }
        ]
    }

async def send_slack_message():
    """Send the optimized message to Slack"""
    
    # Check if webhook URL is configured
    if not SLACK_WEBHOOK_URL or 'YOUR/WEBHOOK/URL' in SLACK_WEBHOOK_URL:
        print("❌ Please set your SLACK_WEBHOOK_URL environment variable")
        print("   export SLACK_WEBHOOK_URL='https://hooks.slack.com/services/YOUR/ACTUAL/WEBHOOK'")
        return False

    print(f"📤 Sending to Slack webhook: {SLACK_WEBHOOK_URL[:50]}...")
    
    message = create_optimized_slack_message()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(SLACK_WEBHOOK_URL, json=message) as response:
                if response.status == 200:
                    print("✅ Optimized Slack notification sent successfully!")
                    print(f"📱 Check your Slack channel for the improved UI")
                    return True
                else:
                    print(f"❌ Failed to send Slack notification: {response.status}")
                    response_text = await response.text()
                    print(f"Response: {response_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Error sending Slack notification: {e}")
        return False

def print_message_preview():
    """Print a preview of the message structure"""
    message = create_optimized_slack_message()
    print("🎨 Optimized Slack Message Preview:")
    print("=" * 50)
    print(json.dumps(message, indent=2))
    print("\n" + "=" * 50)

async def main():
    """Main function"""
    print("🚀 Triggering Optimized Slack UI Test")
    print("=" * 40)
    
    # Show preview
    print_message_preview()
    
    # Send to Slack
    print("\n📤 Sending to Slack...")
    success = await send_slack_message()
    
    if success:
        print("\n✨ Test completed successfully!")
        print("💡 The new UI features:")
        print("   • Professional header with urgency indicators")
        print("   • Structured field layout for easy scanning")
        print("   • Visual risk assessment with emojis")
        print("   • Context sections for incident details")
        print("   • Enhanced action buttons with confirmations")
        print("   • Consistent branding and timestamps")
    else:
        print("\n❌ Test failed - check your webhook URL configuration")

if __name__ == "__main__":
    asyncio.run(main())
