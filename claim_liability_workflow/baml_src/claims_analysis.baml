class ClaimClassification {
  claimType "auto" | "property" | "liability" | "professional" | "bodily_injury" @description("The type of insurance claim")
  severity "minor" | "major" | "catastrophic" @description("Severity level of the claim")
  estimatedValue float @description("Estimated claim value in CAD")
  confidenceScore float @description("AI confidence in the classification (0-1)")
}

class FraudAnalysis {
  riskScore int @description("Fraud risk score from 0-100")
  redFlags string[] @description("List of identified red flags")
  fraudIndicators string[] @description("Specific fraud indicators found")
  recommendation "approve" | "investigate" | "deny" @description("AI recommendation based on fraud analysis")
  confidenceScore float @description("Confidence in fraud assessment (0-1)")
}

class CoverageAnalysis {
  policyValid bool @description("Whether the policy is valid and active")
  coverageType string @description("Type of coverage applicable")
  deductible float @description("Applicable deductible amount")
  coverageLimit float @description("Maximum coverage limit")
  exclusions string[] @description("List of coverage exclusions")
  recommendation "covered" | "partially_covered" | "not_covered" @description("Coverage recommendation")
  policyPeriod string @description("Policy period and validity")
  termsConditions string @description("Relevant policy terms and conditions")
}

class DocumentAnalysis {
  documentType "police_report" | "medical_record" | "receipt" | "photo" | "insurance_certificate" | "other" @description("Type of document")
  extractedData map<string, string> @description("Key-value pairs of extracted data")
  completeness float @description("Document completeness score (0-1)")
  quality float @description("Document quality score (0-1)")
  requiredForClaim bool @description("Whether this document is required for the claim")
  authenticityAssessment string @description("Assessment of document authenticity")
}

class LiabilityAssessment {
  faultDetermination "clear_liability" | "shared_liability" | "no_liability" | "unclear" @description("Liability determination")
  faultPercentage float @description("Percentage of fault assigned (0-100)")
  contributingFactors string[] @description("Factors contributing to the incident")
  evidenceStrength "strong" | "moderate" | "weak" @description("Strength of evidence")
  recommendation string @description("AI recommendation for liability")
  canadianLiabilityContext string @description("Canadian liability law considerations")
}

class LossQuantumAssessment {
  totalLossValue float @description("Total estimated loss value in CAD")
  propertyDamageValue float @description("Property damage value if applicable")
  bodilyInjuryValue float @description("Bodily injury value if applicable")
  otherDamagesValue float @description("Other damages value")
  supportingEvidence string[] @description("Evidence supporting the quantum assessment")
  confidenceLevel "low" | "medium" | "high" @description("Confidence in quantum assessment")
}

class ClaimAnalysis {
  classification ClaimClassification
  fraudAnalysis FraudAnalysis
  coverageAnalysis CoverageAnalysis
  documents DocumentAnalysis[]
  liabilityAssessment LiabilityAssessment
  lossQuantum LossQuantumAssessment
  overallRecommendation "approve" | "deny" | "investigate" | "request_more_info" @description("Overall AI recommendation")
  reasoning string @description("Detailed reasoning for the recommendation")
  nextSteps string[] @description("Recommended next steps")
  priority "low" | "medium" | "high" | "urgent" @description("Claim priority level")
  businessContext string @description("Business context and considerations")
}

function AnalyzeClaim(
  emailContent: string,
  attachments: string[],
  ocrResults: string[]
) -> ClaimAnalysis {
  client OpenAIGPT4O
  
  prompt #"
    You are an expert insurance claims analyst specializing in Canadian liability claims processing for Zurich Insurance.
    
    BUSINESS CONTEXT:
    - Claims is a key technical function within Zurich responsible for assessing coverage of claims
    - Casualty claims involve third parties claiming damage/loss due to insured customer actions/inactions
    - Common examples: slip-and-falls in stores, property damage affecting third parties, water leaks, fires
    - Coverage determination involves investigating primary cause and checking policy terms/conditions/period
    - Quantum assessment ensures claimant restoration to pre-incident condition without loss or gain
    - Fault allocation affects claim value - Zurich responsible only for insured's fault percentage
    - Split decisions are common when responsibility must be shared
    
    PROCESS REQUIREMENTS:
    1. Identify cause of loss and record in analysis
    2. Determine if loss cause is covered under policy terms, conditions, and period
    3. For covered losses, assess fault percentage attributable to insured customer
    4. Determine loss quantum considering fault percentage
    5. If insufficient data, indicate and recommend additional documentation
    
    Analyze the following claim information and provide comprehensive assessment:
    
    Email Content:
    {{ emailContent }}
    
    Attachments:
    {{ attachments }}
    
    OCR Results:
    {{ ocrResults }}
    
    Provide detailed analysis including:
    1. Claim classification and severity assessment
    2. Fraud risk analysis with specific Canadian insurance fraud indicators
    3. Coverage analysis against policy terms, conditions, and period
    4. Document analysis and authenticity assessment
    5. Liability assessment with Canadian liability law considerations
    6. Loss quantum assessment with fault percentage consideration
    7. Overall recommendation with business context
    
    Focus on Canadian insurance regulations, liability laws, and Zurich's claims processing standards.
    Be thorough in identifying potential fraud indicators, coverage issues, and fault allocation.
    Consider the business requirement to restore claimants to pre-incident condition.
    
    {{ ctx.output_format }}
  "#
}

class EmailClassification {
  isClaim bool @description("Whether this email contains a claim")
  claimType "auto" | "property" | "liability" | "professional" | "bodily_injury" | "general_inquiry" | "general" | "other" @description("Type of claim or inquiry")
  urgency "low" | "medium" | "high" | "urgent" @description("Urgency level")
  requiresImmediateResponse bool @description("Whether immediate response is needed")
  suggestedResponse string @description("Suggested response template")
}

function ClassifyEmail(
  subject: string,
  body: string,
  hasAttachments: bool
) -> EmailClassification {
  client OpenAIGPT4O
  
  prompt #"
    You are an email classification system for Zurich Insurance claims processing.
    
    Classify the following email to determine if it's a claim and what type:
    
    Subject: {{ subject }}
    Body: {{ body }}
    Has Attachments: {{ hasAttachments }}
    
    Determine:
    1. Is this a claim or general inquiry?
    2. What type of claim (if applicable)?
    3. Urgency level
    4. Whether immediate response is needed
    5. Suggested response template
    
    Focus on Canadian insurance terminology and claim patterns.
    
    {{ ctx.output_format }}
  "#
}

class DocumentExtraction {
  policyNumber string? @description("Extracted policy number")
  claimAmount float? @description("Claim amount mentioned")
  incidentDate string? @description("Date of incident")
  location string? @description("Location of incident")
  partiesInvolved string[] @description("Names of parties involved")
  damages string[] @description("Types of damages mentioned")
  witnesses string[] @description("Witness information")
  policeReportNumber string? @description("Police report number if mentioned")
  medicalInformation string[] @description("Medical details if applicable")
}

function ExtractClaimDetails(
  emailContent: string,
  ocrText: string
) -> DocumentExtraction {
  client OpenAIGPT4O
  
  prompt #"
    Extract key claim details from the provided email content and OCR text.
    
    Email Content:
    {{ emailContent }}
    
    OCR Text:
    {{ ocrText }}
    
    Extract the following information:
    - Policy number
    - Claim amount
    - Incident date
    - Location
    - Parties involved
    - Types of damages
    - Witness information
    - Police report number
    - Medical information (if applicable)
    
    Be thorough and extract all relevant information for claims processing.
    
    {{ ctx.output_format }}
  "#
} 