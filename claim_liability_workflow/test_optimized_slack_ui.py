#!/usr/bin/env python3
"""
Test script for the optimized Slack UI
Demonstrates the improved Block Kit notification design
"""

import asyncio
import json
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, Any, Optional

# Mock data structures for testing
@dataclass
class MockClassification:
    claimType: str = "BODILY_INJURY"
    urgency: str = "MEDIUM"
    isClaim: bool = True

@dataclass
class MockCoverageAnalysis:
    recommendation: str = "covered"

@dataclass
class MockFraudAnalysis:
    riskScore: int = 25

@dataclass
class MockLiabilityAssessment:
    faultPercentage: int = 80

@dataclass
class MockAIAnalysis:
    coverageAnalysis: MockCoverageAnalysis
    fraudAnalysis: MockFraudAnalysis
    liabilityAssessment: MockLiabilityAssessment
    estimatedValue: float = 10000.00

@dataclass
class MockExtractedDetails:
    incidentDate: str = "August 27, 2020"
    location: str = "No Frills location"

@dataclass
class MockClaimData:
    claim_id: str
    classification: MockClassification
    email_data: Dict[str, Any]
    ai_analysis: Optional[MockAIAnalysis]
    extracted_details: MockExtractedDetails
    created_at: datetime

def create_test_claim_data() -> MockClaimData:
    """Create test claim data for UI demonstration"""
    return MockClaimData(
        claim_id="CLAIM-2025-08FEF3DB",
        classification=MockClassification(),
        email_data={
            'from_email': '<EMAIL>',
            'from_name': 'Dinesh Krishna',
            'subject': 'Bodily Injury Claim - Incident at No Frills',
            'attachments': [
                {'filename': 'incident_report.pdf'},
                {'filename': 'medical_records.pdf'},
                {'filename': 'witness_statement.pdf'},
                {'filename': 'photos.zip'}
            ]
        },
        ai_analysis=MockAIAnalysis(
            coverageAnalysis=MockCoverageAnalysis(),
            fraudAnalysis=MockFraudAnalysis(),
            liabilityAssessment=MockLiabilityAssessment()
        ),
        extracted_details=MockExtractedDetails(),
        created_at=datetime.now()
    )

def generate_optimized_slack_blocks(claim_data: MockClaimData, customer_name: str) -> Dict[str, Any]:
    """Generate the optimized Slack Block Kit message"""
    
    # Extract claim information
    claim_id = claim_data.claim_id
    claim_type = claim_data.classification.claimType.replace('_', ' ').title()
    urgency = claim_data.classification.urgency.title()
    customer_email = claim_data.email_data.get('from_email', 'N/A')
    
    # Calculate estimated value
    estimated_value = f"${claim_data.ai_analysis.estimatedValue:,.2f} CAD"
    
    # Get analysis data
    analysis = claim_data.ai_analysis
    coverage_status = analysis.coverageAnalysis.recommendation
    fraud_risk = f"{analysis.fraudAnalysis.riskScore}/100"
    liability_percentage = f"{analysis.liabilityAssessment.faultPercentage}% insured fault"
    
    # Determine urgency emoji and color
    urgency_emoji = "🔴" if urgency == "High" else "🟡" if urgency == "Medium" else "🟢"
    
    # Create optimized Block Kit message
    blocks = [
        # Header with urgency indicator
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": f"{urgency_emoji} New Claim Assignment Required"
            }
        },
        # Main claim information
        {
            "type": "section",
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": f"*Claim ID:*\n`{claim_id}`"
                },
                {
                    "type": "mrkdwn", 
                    "text": f"*Type:*\n{claim_type}"
                },
                {
                    "type": "mrkdwn",
                    "text": f"*Urgency:*\n{urgency_emoji} {urgency}"
                },
                {
                    "type": "mrkdwn",
                    "text": f"*Estimated Value:*\n{estimated_value}"
                }
            ]
        },
        # Customer information
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Customer:* {customer_name}\n*Email:* {customer_email}"
            }
        },
        # Divider
        {
            "type": "divider"
        },
        # Analysis overview with context
        {
            "type": "context",
            "elements": [
                {
                    "type": "mrkdwn",
                    "text": f"📅 *Incident:* {claim_data.extracted_details.incidentDate}"
                }
            ]
        },
        {
            "type": "context", 
            "elements": [
                {
                    "type": "mrkdwn",
                    "text": f"📍 *Location:* {claim_data.extracted_details.location}"
                }
            ]
        },
        {
            "type": "context",
            "elements": [
                {
                    "type": "mrkdwn",
                    "text": f"📎 *Documents:* {len(claim_data.email_data.get('attachments', []))} files processed"
                }
            ]
        },
        # Divider
        {
            "type": "divider"
        },
        # Risk assessment section
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "*Risk Assessment:*"
            },
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": f"*Coverage:*\n✅ {coverage_status}"
                },
                {
                    "type": "mrkdwn", 
                    "text": f"*Fraud Risk:*\n✅ {fraud_risk}"
                },
                {
                    "type": "mrkdwn",
                    "text": f"*Liability:*\n🔴 {liability_percentage}"
                }
            ]
        },
        # Divider
        {
            "type": "divider"
        },
        # Action buttons
        {
            "type": "actions",
            "elements": [
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "🔍 Review Claim",
                        "emoji": True
                    },
                    "style": "primary",
                    "url": f"http://localhost:8080/review/{claim_id}",
                    "action_id": "review_claim"
                },
                {
                    "type": "button", 
                    "text": {
                        "type": "plain_text",
                        "text": "👤 Assign to Me",
                        "emoji": True
                    },
                    "style": "danger",
                    "action_id": f"assign_to_me_{claim_id}",
                    "confirm": {
                        "title": {
                            "type": "plain_text",
                            "text": "Confirm Assignment"
                        },
                        "text": {
                            "type": "mrkdwn",
                            "text": f"Are you sure you want to assign claim `{claim_id}` to yourself?"
                        },
                        "confirm": {
                            "type": "plain_text",
                            "text": "Yes, Assign"
                        },
                        "deny": {
                            "type": "plain_text",
                            "text": "Cancel"
                        }
                    }
                },
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text", 
                        "text": "📋 View Details",
                        "emoji": True
                    },
                    "action_id": f"view_details_{claim_id}"
                }
            ]
        },
        # Footer context
        {
            "type": "context",
            "elements": [
                {
                    "type": "mrkdwn",
                    "text": f"⏰ Received at {claim_data.created_at.strftime('%I:%M %p')} | 🏢 Zurich Insurance Claims"
                }
            ]
        }
    ]
    
    return {
        "text": f"New Claim Assignment Required: {claim_id}",
        "blocks": blocks
    }

def main():
    """Main function to demonstrate the optimized UI"""
    print("🎨 Optimized Slack UI for Claims Processing")
    print("=" * 50)
    
    # Create test data
    claim_data = create_test_claim_data()
    customer_name = "Dinesh Krishna"
    
    # Generate optimized Slack message
    slack_message = generate_optimized_slack_blocks(claim_data, customer_name)
    
    # Pretty print the JSON for review
    print("\n📱 Optimized Slack Block Kit Message:")
    print("-" * 40)
    print(json.dumps(slack_message, indent=2))
    
    print("\n✨ Key UI Improvements:")
    print("• Professional header with urgency indicators")
    print("• Structured field layout for easy scanning")
    print("• Visual risk assessment with emojis")
    print("• Context sections for incident details")
    print("• Enhanced action buttons with confirmations")
    print("• Consistent branding and timestamps")
    print("• Better error handling and fallbacks")
    
    print(f"\n🔗 Review URL: http://localhost:8080/review/{claim_data.claim_id}")

if __name__ == "__main__":
    main()
